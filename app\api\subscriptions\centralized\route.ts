/**
 * CENTRALIZED SUBSCRIPTION API ENDPOINT
 * 
 * This endpoint provides a single entry point for all subscription operations
 * using the centralized SubscriptionFlowManager logic.
 */

import { NextRequest, NextResponse } from "next/server";
import { processSubscription } from "@/lib/actions/subscription/centralized";

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { planId, planCycle } = body;

    // Validate required parameters
    if (!planId || !planCycle) {
      return NextResponse.json(
        {
          success: false,
          error: "Missing required parameters: planId and planCycle"
        },
        { status: 400 }
      );
    }

    // Validate plan cycle
    if (!['monthly', 'yearly'].includes(planCycle)) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid plan cycle. Must be 'monthly' or 'yearly'"
        },
        { status: 400 }
      );
    }

    // Process subscription using centralized logic
    const result = await processSubscription(planId, planCycle);

    // Return result
    if (result.success) {
      return NextResponse.json({
        success: true,
        message: result.message,
        data: {
          subscription_id: result.subscriptionId,
          payment_required: result.paymentRequired,
          ...result.data
        }
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          error: result.error || result.message
        },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Error processing subscription:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Internal server error"
      },
      { status: 500 }
    );
  }
}
