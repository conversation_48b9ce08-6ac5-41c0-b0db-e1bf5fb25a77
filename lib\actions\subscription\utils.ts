"use server";

import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";
import { ActionResponse } from "./types";

/**
 * Get the current authenticated user
 * @returns The user object or null if not authenticated
 */
export async function getCurrentUser() {
  const supabase = await createClient();
  const { data, error } = await supabase.auth.getUser();

  if (error || !data.user) {
    return null;
  }

  return data.user;
}

/**
 * Get the business profile for the current user
 * @param userId The user ID
 * @param select The fields to select
 * @returns The business profile or null if not found
 */
export async function getBusinessProfile(
  userId: string,
  select: string = "*"
): Promise<BusinessProfileType | null> {
  const supabase = await createClient();

  // First check if the profile exists at all
  const { count, error: countError } = await supabase
    .from("business_profiles")
    .select("*", { count: "exact", head: true })
    .eq("id", userId);

  if (countError) {
    console.error(`Error checking if business profile exists for user ${userId}:`, countError);
  } else if (count === 0) {
    console.error(`Business profile does not exist for user ${userId}`);
    return null;
  }

  // Ensure id is always included in the select statement
  let selectWithId = select;
  if (select !== "*" && !select.includes("id")) {
    selectWithId = `id, ${select}`;
  }

  // Now fetch the actual profile with the requested fields
  const { data, error } = await supabase
    .from("business_profiles")
    .select(selectWithId)
    .eq("id", userId)
    .single();

  if (error) {
    console.error(`Error fetching business profile for user ${userId}:`, error);
    return null;
  }

  if (!data) {
    console.error(`No business profile data returned for user ${userId}`);
    return null;
  }

  // Ensure we have a valid object with an id property
  let profileData: Record<string, unknown>;

  if (typeof data === 'object' && data !== null) {
    // Create a new object with the data
    profileData = { ...(data as Record<string, unknown>) };

    // Add id if it doesn't exist
    if (!('id' in profileData)) {
      profileData.id = userId;
    }
  } else {
    // If data is not an object for some reason, create a minimal valid profile
    console.error(`Profile data is not an object: ${typeof data}`);
    profileData = { id: userId };
  }

  return profileData as unknown as BusinessProfileType;
}

// Define types for user and profile
export interface UserType {
  id: string;
  email?: string;
  [key: string]: unknown;
}

export interface BusinessProfileType {
  id: string;
  trial_end_date?: string | null;
  has_active_subscription?: boolean | null;
  subscription_paused_at?: string | null;
  cancellation_requested_at?: string | null;
  [key: string]: unknown;
}

export interface PaymentSubscriptionType {
  id: string;
  business_profile_id: string;
  razorpay_subscription_id?: string | null;
  subscription_status: string;
  plan_id: string;
  plan_cycle: string;
  [key: string]: unknown;
}

/**
 * Get the user's subscription data
 * @param userId The user ID
 * @param select The fields to select
 * @returns The subscription data or null if not found
 */
export async function getUserSubscription(
  userId: string,
  select: string = "*"
): Promise<Record<string, unknown> | null> {
  const supabase = await createClient();

  // Ensure id is always included in the select statement
  let selectWithId = select;
  if (select !== "*" && !select.includes("id")) {
    selectWithId = `id, ${select}`;
  }

  // Remove has_active_subscription from select if present
  // This field exists in business_profiles, not in payment_subscriptions
  if (selectWithId.includes("has_active_subscription")) {
    selectWithId = selectWithId.replace("has_active_subscription", "");
    // Clean up any double commas that might result
    selectWithId = selectWithId.replace(/,\s*,/g, ",");
    // Remove trailing comma if present
    selectWithId = selectWithId.replace(/,\s*$/g, "");
    // Remove leading comma if present
    selectWithId = selectWithId.replace(/^\s*,/, "");
  }

  // Fetch the subscription with the requested fields
  const { data, error } = await supabase
    .from("payment_subscriptions")
    .select(selectWithId)
    .eq("business_profile_id", userId)
    .order("created_at", { ascending: false })
    .limit(1)
    .maybeSingle();

  if (error) {
    console.error(`Error fetching subscription for user ${userId}:`, error);
    return null;
  }

  if (!data) {
    return null;
  }

  // Ensure we return a valid Record<string, unknown> object
  if (typeof data === 'object' && data !== null) {
    return data as Record<string, unknown>;
  }

  // If data is not an object, return null
  console.error(`Subscription data is not an object: ${typeof data}`);
  return null;
}

/**
 * Check if the user is authenticated and has a business profile
 * @param select The fields to select from the subscription
 * @returns An object with user, profile, subscription, or an error response
 */
export async function getUserAndProfile(
  select: string = "*"
): Promise<
  | { user: UserType; profile: BusinessProfileType; subscription: Record<string, unknown> | null; error: null }
  | { user: UserType | null; profile: null; subscription: null; error: string }
> {
  const user = await getCurrentUser();

  if (!user) {
    console.error("No user found in auth session");
    return { user: null, profile: null, subscription: null, error: "User not authenticated" };
  }

  // Extract fields that should be fetched from business_profiles instead of payment_subscriptions
  const profileFields = ["trial_end_date", "subscription_start_date", "cancellation_requested_at", "subscription_paused_at"];
  let profileSelect = "id, has_active_subscription";
  let subscriptionSelect = select;

  // If select is not "*", check for profile fields in the select string
  if (select !== "*") {
    // Split the select string by commas and trim each field
    const fields = select.split(',').map(field => field.trim());

    // Filter out profile fields from subscription select
    const subscriptionFields = fields.filter(field => !profileFields.includes(field));

    // Add profile fields to profile select
    const additionalProfileFields = fields.filter(field => profileFields.includes(field));
    if (additionalProfileFields.length > 0) {
      profileSelect += `, ${additionalProfileFields.join(', ')}`;
    }

    // Update subscription select
    if (subscriptionFields.length > 0) {
      subscriptionSelect = subscriptionFields.join(', ');
    } else {
      subscriptionSelect = "id"; // Minimum field to select
    }
  }

  const profile = await getBusinessProfile(user.id, profileSelect);

  if (!profile) {
    console.error(`Could not fetch business profile for user ${user.id}`);

    // Instead of creating a profile, just return an error
    // This is because profile creation is handled elsewhere in the application
    const userType: UserType = { ...user };
    return {
      user: userType,
      profile: null,
      subscription: null,
      error: "Business profile not found. Please complete onboarding first."
    };
  }

  // Get subscription data if requested
  const subscription = await getUserSubscription(user.id, subscriptionSelect);

  // Convert user to UserType
  const userType: UserType = {
    ...user // Spread all properties
  };

  return { user: userType, profile, subscription, error: null };
}

/**
 * Check if the user is on trial using centralized logic
 * @param profile The business profile
 * @param subscription The payment subscription (optional)
 * @returns Whether the user is on trial
 */
export async function isUserOnTrial(
  profile: BusinessProfileType,
  subscription?: PaymentSubscriptionType | null
): Promise<boolean> {
  // Import centralized manager
  const { SubscriptionStateManager } = await import('@/lib/razorpay/webhooks/handlers/utils');

  // If we have subscription data, use centralized logic
  if (subscription) {
    return SubscriptionStateManager.isTrialStatus(subscription.subscription_status);
  }

  // Fallback to date-based check for backward compatibility
  const trialEndDate = profile.trial_end_date
    ? new Date(profile.trial_end_date)
    : null;

  return trialEndDate !== null && trialEndDate > new Date();
}

/**
 * Revalidate subscription-related paths
 */
export async function revalidateSubscriptionPaths(): Promise<void> {
  revalidatePath("/dashboard/business/plan");
  revalidatePath("/dashboard/business");
}

/**
 * Create a standard error response
 * @param error The error message
 * @returns The error response
 */
export async function createErrorResponse(error: string): Promise<ActionResponse> {
  return { success: false, error };
}

/**
 * Create a standard success response
 * @param data The response data
 * @returns The success response
 */
export async function createSuccessResponse<T>(data?: T): Promise<ActionResponse<T>> {
  return { success: true, data };
}

/**
 * Create a standard error response (synchronous version)
 * @param error The error message
 * @returns The error response
 */
export async function createErrorResponseSync<T = Record<string, unknown>>(error: string): Promise<ActionResponse<T>> {
  return { success: false, error };
}

/**
 * Create a standard success response (synchronous version)
 * @param data The response data
 * @returns The success response
 */
export async function createSuccessResponseSync<T>(data?: T): Promise<ActionResponse<T>> {
  return { success: true, data };
}

/**
 * Check if the subscription belongs to the user
 * @param userId The user ID
 * @param subscriptionId The subscription ID
 * @returns Whether the subscription belongs to the user
 */
export async function doesSubscriptionBelongToUser(
  userId: string,
  subscriptionId: string
): Promise<boolean> {
  const supabase = await createClient();

  // Check if the subscription exists in the payment_subscriptions table
  const { data, error } = await supabase
    .from("payment_subscriptions")
    .select("id")
    .eq("business_profile_id", userId)
    .eq("razorpay_subscription_id", subscriptionId)
    .maybeSingle();

  if (error) {
    console.error(`Error checking subscription ownership: ${error.message}`);
    return false;
  }

  return data !== null;
}
