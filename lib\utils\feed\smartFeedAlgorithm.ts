import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';
import { groupPostsByAuthor, roundRobinDistribution } from './diversityEngine';
import { createBusinessPriorityGroups, distributePrioritizedBusinessPosts } from './planPrioritizer';
import { mergeCustomerAndBusinessPosts } from './feedMerger';

/**
 * Smart Feed Algorithm - Main orchestrator
 * Combines all feed optimization strategies in a clean, maintainable way
 * 
 * Approach inspired by major social media platforms:
 * 1. Customer posts: Chronological with diversity (like Instagram Stories)
 * 2. Business posts: Plan-prioritized with diversity (like LinkedIn sponsored content)
 * 3. Merge: Intelligent interleaving (like Facebook News Feed)
 */

export interface SmartFeedOptions {
  prioritizeBusinessPosts?: boolean;
  maintainChronologicalFlow?: boolean;
  enableDiversity?: boolean;
  businessPostWeight?: number;
}

/**
 * Main smart feed algorithm
 * Clean, simple interface that orchestrates all the complex logic
 */
export function processSmartFeed(
  posts: UnifiedPost[],
  options: SmartFeedOptions = {}
): UnifiedPost[] {
  const {
    prioritizeBusinessPosts = true,
    maintainChronologicalFlow = true,
    enableDiversity = true,
    businessPostWeight = 0.6
  } = options;

  if (posts.length === 0) return [];

  // Separate customer and business posts
  const customerPosts = posts.filter(post => post.post_source === 'customer');
  const businessPosts = posts.filter(post => post.post_source === 'business');

  // Process customer posts - maintain chronological order with diversity
  const processedCustomerPosts = processCustomerPosts(customerPosts, enableDiversity);

  // Process business posts - apply plan prioritization with diversity
  const processedBusinessPosts = processBusinessPosts(businessPosts, prioritizeBusinessPosts, enableDiversity);

  // Merge both types intelligently
  return mergeCustomerAndBusinessPosts(
    processedCustomerPosts,
    processedBusinessPosts,
    {
      maintainChronologicalFlow,
      diversityEnabled: enableDiversity,
      businessPostWeight
    }
  );
}

/**
 * Process customer posts - keep chronological order, add diversity
 */
function processCustomerPosts(customerPosts: UnifiedPost[], enableDiversity: boolean): UnifiedPost[] {
  if (customerPosts.length === 0) return [];

  if (!enableDiversity) {
    // Simple chronological sort
    return customerPosts.sort((a, b) => 
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
  }

  // Group by author and apply round-robin for diversity
  const groupedPosts = groupPostsByAuthor(customerPosts);
  return roundRobinDistribution(groupedPosts);
}

/**
 * Process business posts - apply plan prioritization and diversity
 */
function processBusinessPosts(
  businessPosts: UnifiedPost[], 
  prioritizeByPlan: boolean, 
  enableDiversity: boolean
): UnifiedPost[] {
  if (businessPosts.length === 0) return [];

  if (!prioritizeByPlan) {
    // Simple chronological processing like customer posts
    return processCustomerPosts(businessPosts, enableDiversity);
  }

  // Apply plan-based prioritization
  const businessGroups = createBusinessPriorityGroups(businessPosts);
  return distributePrioritizedBusinessPosts(businessGroups);
}

/**
 * Apply pagination to processed feed
 */
export function paginateSmartFeed(
  processedPosts: UnifiedPost[],
  page: number = 1,
  limit: number = 10
): UnifiedPost[] {
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  return processedPosts.slice(startIndex, endIndex);
}

/**
 * Get feed statistics for debugging/analytics
 */
export function getFeedStats(posts: UnifiedPost[]): {
  totalPosts: number;
  customerPosts: number;
  businessPosts: number;
  planDistribution: Record<string, number>;
  uniqueAuthors: <AUTHORS>
} {
  const customerPosts = posts.filter(p => p.post_source === 'customer').length;
  const businessPosts = posts.filter(p => p.post_source === 'business').length;
  
  const planDistribution: Record<string, number> = {};
  const uniqueAuthors = new Set<string>();
  
  posts.forEach(post => {
    uniqueAuthors.add(post.author_id);
    
    if (post.post_source === 'business') {
      const plan = post.business_plan || 'free';
      planDistribution[plan] = (planDistribution[plan] || 0) + 1;
    }
  });

  return {
    totalPosts: posts.length,
    customerPosts,
    businessPosts,
    planDistribution,
    uniqueAuthors: <AUTHORS>
  };
}

/**
 * Validate feed quality - ensure diversity rules are followed
 */
export function validateFeedQuality(posts: UnifiedPost[]): {
  isValid: boolean;
  issues: string[];
  diversityScore: number; // 0-1, higher is better
} {
  const issues: string[] = [];
  let consecutiveCount = 0;
  let maxConsecutive = 0;
  let lastAuthorId: string | null = null;
  
  posts.forEach((post, index) => {
    if (post.author_id === lastAuthorId) {
      consecutiveCount++;
      maxConsecutive = Math.max(maxConsecutive, consecutiveCount);
    } else {
      consecutiveCount = 1;
      lastAuthorId = post.author_id;
    }
  });
  
  if (maxConsecutive > 2) {
    issues.push(`Found ${maxConsecutive} consecutive posts from same author`);
  }
  
  // Calculate diversity score (inverse of max consecutive posts)
  const diversityScore = Math.max(0, 1 - (maxConsecutive - 1) / posts.length);
  
  return {
    isValid: issues.length === 0,
    issues,
    diversityScore
  };
}
