import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';

/**
 * Diversity Engine - Ensures no consecutive posts from the same author
 * Inspired by Facebook/Instagram feed algorithms that maintain user engagement
 * through content diversity and prevent feed monotony.
 */

export interface DiversityOptions {
  maxConsecutiveFromSameAuthor?: number;
  prioritizeRecency?: boolean;
}

/**
 * Apply diversity rules to prevent consecutive posts from the same author
 * Uses a sliding window approach similar to major social media platforms
 */
export function applyDiversityRules(
  posts: UnifiedPost[], 
  options: DiversityOptions = {}
): UnifiedPost[] {
  const { maxConsecutiveFromSameAuthor = 1, prioritizeRecency = true } = options;
  
  if (posts.length <= 1) return posts;

  const diversifiedPosts: UnifiedPost[] = [];
  const remainingPosts = [...posts];
  let lastAuthorId: string | null = null;
  let consecutiveCount = 0;

  while (remainingPosts.length > 0) {
    let selectedIndex = -1;
    
    // First, try to find a post from a different author
    for (let i = 0; i < remainingPosts.length; i++) {
      const post = remainingPosts[i];
      
      if (post.author_id !== lastAuthorId) {
        selectedIndex = i;
        break;
      }
    }
    
    // If no different author found, or we haven't exceeded consecutive limit
    if (selectedIndex === -1 && consecutiveCount < maxConsecutiveFromSameAuthor) {
      selectedIndex = 0; // Take the first available post
    }
    
    // If still no selection, force diversity by taking first different author
    if (selectedIndex === -1) {
      for (let i = 0; i < remainingPosts.length; i++) {
        if (remainingPosts[i].author_id !== lastAuthorId) {
          selectedIndex = i;
          break;
        }
      }
      // If still no different author, take first available (edge case)
      if (selectedIndex === -1) selectedIndex = 0;
    }

    const selectedPost = remainingPosts.splice(selectedIndex, 1)[0];
    diversifiedPosts.push(selectedPost);
    
    // Update tracking variables
    if (selectedPost.author_id === lastAuthorId) {
      consecutiveCount++;
    } else {
      consecutiveCount = 1;
      lastAuthorId = selectedPost.author_id;
    }
  }

  return diversifiedPosts;
}

/**
 * Group posts by author while maintaining chronological order within groups
 */
export function groupPostsByAuthor(posts: UnifiedPost[]): Map<string, UnifiedPost[]> {
  const grouped = new Map<string, UnifiedPost[]>();
  
  posts.forEach(post => {
    if (!grouped.has(post.author_id)) {
      grouped.set(post.author_id, []);
    }
    grouped.get(post.author_id)!.push(post);
  });
  
  // Sort posts within each group chronologically (latest first)
  grouped.forEach((authorPosts, authorId) => {
    grouped.set(authorId, authorPosts.sort((a, b) => 
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    ));
  });
  
  return grouped;
}

/**
 * Round-robin distribution to ensure fair representation
 * Similar to how Instagram distributes stories from different accounts
 */
export function roundRobinDistribution(groupedPosts: Map<string, UnifiedPost[]>): UnifiedPost[] {
  const result: UnifiedPost[] = [];
  const queues = Array.from(groupedPosts.entries()).map(([authorId, posts]) => ({
    authorId,
    posts: [...posts]
  }));

  // Continue until all queues are empty
  while (queues.some(queue => queue.posts.length > 0)) {
    queues.forEach(queue => {
      if (queue.posts.length > 0) {
        const post = queue.posts.shift()!;
        result.push(post);
      }
    });
  }

  return result;
}
