import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';
import { PLAN_PRIORITY } from './planPrioritizer';
import { applyDiversityRules } from './diversityEngine';

/**
 * Optimized Hybrid Algorithm for Exact Post Count
 * 
 * Works with exactly the fetched posts (e.g., 10 posts) without losing any content
 * Strategy:
 * 1. Separate customer and business posts
 * 2. Apply plan prioritization to business posts
 * 3. Merge customer + business posts by timestamp
 * 4. Apply diversity rules
 * 5. Return all posts (no loss)
 */

export interface OptimizedHybridOptions {
  enableDiversity?: boolean;
  maintainChronologicalFlow?: boolean;
}

/**
 * Main optimized hybrid algorithm - processes exactly the fetched posts
 */
export function processOptimizedHybrid(
  posts: UnifiedPost[],
  options: OptimizedHybridOptions = {}
): UnifiedPost[] {
  const {
    enableDiversity = true,
    maintainChronologicalFlow = true
  } = options;

  if (posts.length === 0) return [];

  // Separate customer and business posts
  const customerPosts = posts.filter(post => post.post_source === 'customer');
  const businessPosts = posts.filter(post => post.post_source === 'business');

  // Process customer posts (maintain chronological order)
  const processedCustomerPosts = processCustomerPostsOptimized(customerPosts);

  // Process business posts (apply plan prioritization)
  const processedBusinessPosts = processBusinessPostsOptimized(businessPosts);

  // Merge both types
  const mergedPosts = mergeOptimizedPosts(
    processedCustomerPosts,
    processedBusinessPosts,
    maintainChronologicalFlow
  );

  // Apply diversity rules if enabled
  return enableDiversity ? applyDiversityRules(mergedPosts) : mergedPosts;
}

/**
 * Process customer posts - simple chronological sort
 */
function processCustomerPostsOptimized(customerPosts: UnifiedPost[]): UnifiedPost[] {
  if (customerPosts.length === 0) return [];

  // Sort chronologically (latest first)
  return customerPosts.sort((a, b) => 
    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  );
}

/**
 * Process business posts - apply plan prioritization ONLY to latest post per business
 * Other posts from same business compete purely on timestamp
 */
function processBusinessPostsOptimized(businessPosts: UnifiedPost[]): UnifiedPost[] {
  if (businessPosts.length === 0) return [];

  // Group posts by business (author_id)
  const postsByBusiness = new Map<string, UnifiedPost[]>();
  businessPosts.forEach(post => {
    if (!postsByBusiness.has(post.author_id)) {
      postsByBusiness.set(post.author_id, []);
    }
    postsByBusiness.get(post.author_id)!.push(post);
  });

  // Sort posts within each business by timestamp (latest first)
  postsByBusiness.forEach((posts, businessId) => {
    postsByBusiness.set(businessId, posts.sort((a, b) =>
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    ));
  });

  // Separate latest posts (get plan priority) from other posts (time-based only)
  const latestPostsPerBusiness: UnifiedPost[] = [];
  const otherPostsFromBusinesses: UnifiedPost[] = [];

  postsByBusiness.forEach((posts, businessId) => {
    if (posts.length > 0) {
      // First post is latest (already sorted)
      latestPostsPerBusiness.push(posts[0]);

      // Rest are other posts from same business
      if (posts.length > 1) {
        otherPostsFromBusinesses.push(...posts.slice(1));
      }
    }
  });

  // Sort latest posts by plan priority + timestamp
  const prioritizedLatestPosts = latestPostsPerBusiness.sort((a, b) => {
    const planA = a.business_plan || 'free';
    const planB = b.business_plan || 'free';

    const priorityA = PLAN_PRIORITY[planA] || 1;
    const priorityB = PLAN_PRIORITY[planB] || 1;

    // Sort by plan priority first
    if (priorityA !== priorityB) {
      return priorityB - priorityA; // Higher priority first
    }

    // If same plan, sort by timestamp (latest first)
    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
  });

  // Sort other posts purely by timestamp (no plan priority)
  const timeBasedOtherPosts = otherPostsFromBusinesses.sort((a, b) =>
    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  );

  // Return prioritized latest posts first, then time-based other posts
  return [...prioritizedLatestPosts, ...timeBasedOtherPosts];
}

/**
 * Merge customer and business posts with equal treatment
 * No priority between customer vs business - only plan priority within business posts
 */
function mergeOptimizedPosts(
  customerPosts: UnifiedPost[],
  businessPosts: UnifiedPost[],
  maintainChronologicalFlow: boolean
): UnifiedPost[] {
  if (customerPosts.length === 0) return businessPosts;
  if (businessPosts.length === 0) return customerPosts;

  if (maintainChronologicalFlow) {
    // Merge all posts by timestamp - equal treatment
    return [...customerPosts, ...businessPosts]
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  } else {
    // Business posts first (due to plan prioritization), then customer posts
    return [...businessPosts, ...customerPosts];
  }
}

/**
 * Alternative approach: Intelligent interleaving
 * Ensures both customer and business posts get representation
 */
export function processOptimizedHybridWithInterleaving(
  posts: UnifiedPost[],
  options: OptimizedHybridOptions = {}
): UnifiedPost[] {
  const {
    enableDiversity = true,
    maintainChronologicalFlow = true
  } = options;

  if (posts.length === 0) return [];

  // Separate and process posts
  const customerPosts = posts.filter(post => post.post_source === 'customer');
  const businessPosts = posts.filter(post => post.post_source === 'business');

  const processedCustomerPosts = processCustomerPostsOptimized(customerPosts);
  const processedBusinessPosts = processBusinessPostsOptimized(businessPosts);

  // Intelligent interleaving
  const interleavedPosts = intelligentInterleave(
    processedCustomerPosts,
    processedBusinessPosts,
    maintainChronologicalFlow
  );

  // Apply diversity rules if enabled
  return enableDiversity ? applyDiversityRules(interleavedPosts) : interleavedPosts;
}

/**
 * Intelligent interleaving of customer and business posts
 */
function intelligentInterleave(
  customerPosts: UnifiedPost[],
  businessPosts: UnifiedPost[],
  respectTimestamp: boolean
): UnifiedPost[] {
  if (customerPosts.length === 0) return businessPosts;
  if (businessPosts.length === 0) return customerPosts;

  const result: UnifiedPost[] = [];
  let customerIndex = 0;
  let businessIndex = 0;

  // Interleave posts while respecting timestamps if enabled
  while (customerIndex < customerPosts.length || businessIndex < businessPosts.length) {
    const customerPost = customerPosts[customerIndex];
    const businessPost = businessPosts[businessIndex];

    if (!customerPost && businessPost) {
      // Only business posts left
      result.push(businessPost);
      businessIndex++;
    } else if (customerPost && !businessPost) {
      // Only customer posts left
      result.push(customerPost);
      customerIndex++;
    } else if (customerPost && businessPost) {
      // Both available - decide based on timestamp or alternating pattern
      if (respectTimestamp) {
        const customerTime = new Date(customerPost.created_at).getTime();
        const businessTime = new Date(businessPost.created_at).getTime();
        
        if (businessTime >= customerTime) {
          result.push(businessPost);
          businessIndex++;
        } else {
          result.push(customerPost);
          customerIndex++;
        }
      } else {
        // Alternating pattern - business posts get slight preference due to plan prioritization
        if (result.length % 2 === 0) {
          result.push(businessPost);
          businessIndex++;
        } else {
          result.push(customerPost);
          customerIndex++;
        }
      }
    }
  }

  return result;
}

/**
 * Get statistics for the optimized algorithm
 */
export function getOptimizedAlgorithmStats(
  originalPosts: UnifiedPost[],
  processedPosts: UnifiedPost[]
): {
  originalCount: number;
  processedCount: number;
  customerPosts: number;
  businessPosts: number;
  planDistribution: Record<string, number>;
  postsLost: number;
  efficiency: number;
} {
  const planDistribution: Record<string, number> = {};
  
  const businessPosts = processedPosts.filter(p => p.post_source === 'business');
  const customerPosts = processedPosts.filter(p => p.post_source === 'customer');
  
  businessPosts.forEach(post => {
    const plan = post.business_plan || 'free';
    planDistribution[plan] = (planDistribution[plan] || 0) + 1;
  });

  const postsLost = originalPosts.length - processedPosts.length;
  const efficiency = processedPosts.length / originalPosts.length;

  return {
    originalCount: originalPosts.length,
    processedCount: processedPosts.length,
    customerPosts: customerPosts.length,
    businessPosts: businessPosts.length,
    planDistribution,
    postsLost,
    efficiency
  };
}

/**
 * Validate that no posts are lost (should always be 100% with optimized algorithm)
 */
export function validateOptimizedAlgorithm(
  originalPosts: UnifiedPost[],
  processedPosts: UnifiedPost[]
): {
  isValid: boolean;
  issues: string[];
  efficiency: number;
} {
  const issues: string[] = [];
  
  if (originalPosts.length !== processedPosts.length) {
    issues.push(`Post count mismatch: ${originalPosts.length} → ${processedPosts.length}`);
  }

  const originalIds = new Set(originalPosts.map(p => p.id));
  const processedIds = new Set(processedPosts.map(p => p.id));
  
  const lostPosts: string[] = [];
  originalIds.forEach(id => {
    if (!processedIds.has(id)) {
      lostPosts.push(id);
    }
  });

  if (lostPosts.length > 0) {
    issues.push(`Lost posts: ${lostPosts.join(', ')}`);
  }

  const efficiency = processedPosts.length / originalPosts.length;

  return {
    isValid: issues.length === 0,
    issues,
    efficiency
  };
}
