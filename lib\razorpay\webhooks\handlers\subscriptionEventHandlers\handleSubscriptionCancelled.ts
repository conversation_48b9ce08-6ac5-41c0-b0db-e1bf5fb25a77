import { RazorpayWebhookData, RazorpaySubscription } from "../../../types/api";
import { SupabaseClient } from "@supabase/supabase-js";
import { createAdminClient } from "@/utils/supabase/admin";
import {
  extractWebhookTimestamp
} from "../utils";
import { webhookProcessor, type WebhookProcessingContext } from "../webhookProcessor";
import { updateSubscriptionWithBusinessProfile } from "../subscription-db-updater";
import { revertToTrialAtomic, downgradeToFreePlanAtomic } from "../transactionUtils";

/**
 * Handle subscription.cancelled event
 *
 * This event is triggered when a subscription is cancelled.
 * - Authenticated subscriptions: Revert to trial (Plan A cancellation)
 * - Active subscriptions: Downgrade to free plan
 *
 * @param payload The webhook payload
 * @param supabase The Supabase client
 * @returns The result of handling the event
 */
export async function handleSubscriptionCancelled(
  payload: RazorpayWebhookData,
  _supabase: SupabaseClient,
  razorpayEventId?: string
): Promise<{ success: boolean; message: string }> {
  let context: WebhookProcessingContext | null = null;

  try {
    // Extract subscription data from payload
    const subscriptionData = payload.payload.subscription;

    if (!subscriptionData || !subscriptionData.entity) {
      console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload");
      return { success: false, message: "Subscription data not found in payload" };
    }

    // Cast to proper type to access properties
    const subscription = subscriptionData.entity as unknown as RazorpaySubscription;
    const subscriptionId = subscription.id;
    console.log(`[RAZORPAY_WEBHOOK] Subscription cancelled: ${subscriptionId}`);

    // Extract webhook timestamp from Razorpay payload for sequence validation
    const webhookTimestamp = extractWebhookTimestamp(payload);

    // Use centralized webhook processor for race condition protection
    context = {
      subscriptionId,
      eventType: 'subscription.cancelled',
      eventId: razorpayEventId || `cancelled_${subscriptionId}_${Date.now()}`,
      payload: payload as unknown as Record<string, unknown>,
      webhookTimestamp
    };

    const processingResult = await webhookProcessor.processWebhookEvent(context);
    if (!processingResult.shouldProcess) {
      return { success: processingResult.success, message: processingResult.message };
    }

    // Get admin client to bypass RLS
    const adminClient = createAdminClient();

    // Check current subscription status to determine proper handling
    const { data: currentSubscription, error: fetchError } = await adminClient
      .from('payment_subscriptions')
      .select('subscription_status, plan_id, business_profile_id')
      .eq('razorpay_subscription_id', subscriptionId)
      .maybeSingle();

    if (fetchError) {
      console.error(`[RAZORPAY_WEBHOOK] Error fetching subscription ${subscriptionId}:`, fetchError);
      return { success: false, message: `Error fetching subscription: ${fetchError.message}` };
    }

    if (!currentSubscription) {
      console.log(`[RAZORPAY_WEBHOOK] No subscription found for ${subscriptionId}, skipping cancellation processing`);
      return { success: true, message: "No subscription found to cancel" };
    }

    const now = new Date().toISOString();

    // Handle different cancellation scenarios based on current status
    if (currentSubscription.subscription_status === 'authenticated') {
      // Plan A cancellation: Revert authenticated subscription to trial
      console.log(`[RAZORPAY_WEBHOOK] Reverting authenticated subscription ${subscriptionId} to trial (Plan A cancellation)`);

      const revertResult = await revertToTrialAtomic(subscriptionId, currentSubscription.business_profile_id);

      if (revertResult.success) {
        // CRITICAL FIX: Clear Razorpay IDs after successful cancellation
        console.log(`[RAZORPAY_WEBHOOK] Clearing Razorpay IDs for cancelled authenticated subscription ${subscriptionId}`);
        const { clearRazorpayColumnsAfterCancellation } = await import("../transactionUtils");
        const clearResult = await clearRazorpayColumnsAfterCancellation(subscriptionId, currentSubscription.business_profile_id);

        if (!clearResult.success) {
          console.error(`[RAZORPAY_WEBHOOK] Failed to clear Razorpay IDs: ${clearResult.message}`);
          // Continue anyway - the main cancellation was successful
        } else {
          console.log(`[RAZORPAY_WEBHOOK] Successfully cleared Razorpay IDs for subscription ${subscriptionId}`);
        }

        await webhookProcessor.markEventAsSuccess(context.eventId, "Authenticated subscription reverted to trial and Razorpay IDs cleared");
        return { success: true, message: "Authenticated subscription reverted to trial and Razorpay IDs cleared" };
      } else {
        await webhookProcessor.markEventAsFailed(context.eventId, revertResult.message);
        return revertResult;
      }
    } else if (currentSubscription.subscription_status === 'trial' && currentSubscription.plan_id !== 'free') {
      // IDEMPOTENCY FIX: If subscription is already in trial status with a non-free plan,
      // it means this cancellation was already processed (authenticated -> trial)
      // Return success without further processing to maintain idempotency
      console.log(`[RAZORPAY_WEBHOOK] Subscription ${subscriptionId} already in trial status (Plan A cancellation already processed)`);
      await webhookProcessor.markEventAsSuccess(context.eventId, "Authenticated subscription already reverted to trial (idempotent)");
      return { success: true, message: "Authenticated subscription already reverted to trial (idempotent)" };
    } else {
      // For active or other statuses: Downgrade to free plan
      console.log(`[RAZORPAY_WEBHOOK] Downgrading cancelled subscription ${subscriptionId} to free plan`);

      const downgradeResult = await downgradeToFreePlanAtomic(subscriptionId, currentSubscription.business_profile_id, 'cancelled');

      if (downgradeResult.success) {
        await webhookProcessor.markEventAsSuccess(context.eventId, "Subscription cancelled and downgraded to free plan");
        return { success: true, message: "Subscription cancelled and downgraded to free plan" };
      } else {
        await webhookProcessor.markEventAsFailed(context.eventId, downgradeResult.message);
        return downgradeResult;
      }
    }
  } catch (error) {
    console.error("[RAZORPAY_WEBHOOK] Error handling subscription cancelled:", error);
    return {
      success: false,
      message: `Error handling subscription cancelled: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}