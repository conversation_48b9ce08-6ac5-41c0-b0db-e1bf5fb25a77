'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { User, Plus, X, Image as ImageIcon, MessageSquare } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import CustomerPostForm from '@/components/feed/shared/forms/CustomerPostForm';

interface ModernCustomerPostCreatorProps {
  customerName?: string;
  onPostCreated?: () => void;
}

export default function ModernCustomerPostCreator({
  customerName = 'Customer',
  onPostCreated
}: ModernCustomerPostCreatorProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const handlePostSuccess = () => {
    setIsExpanded(false);
    if (onPostCreated) {
      onPostCreated();
    }
  };

  const handleCancel = () => {
    setIsExpanded(false);
  };

  return (
    <Card className="bg-white dark:bg-black border-neutral-200 dark:border-neutral-800 shadow-sm overflow-hidden">
      <CardContent className="p-0">
        <AnimatePresence mode="wait">
          {!isExpanded ? (
            <motion.div
              key="collapsed"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="p-4 md:p-6"
            >
              {/* Mobile Layout: Centered Avatar */}
              <div className="block md:hidden">
                <div className="flex flex-col items-center gap-3">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-[var(--brand-gold)] to-[var(--brand-gold-dark)] flex items-center justify-center">
                    <User className="h-6 w-6 text-[var(--brand-gold-foreground)]" />
                  </div>

                  <Button
                    variant="outline"
                    className="w-full h-10 justify-start text-neutral-500 dark:text-neutral-400 font-normal text-sm bg-neutral-50 dark:bg-neutral-900 border-neutral-200 dark:border-neutral-700 hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-all duration-200"
                    onClick={() => setIsExpanded(true)}
                  >
                    <Plus className="h-4 w-4 mr-2 flex-shrink-0" />
                    <span className="truncate">What&apos;s on your mind, {customerName}?</span>
                  </Button>
                </div>
              </div>

              {/* Desktop/Tablet Layout: Side-by-side */}
              <div className="hidden md:flex items-center gap-3 mb-4">
                <div className="w-12 h-12 rounded-full bg-gradient-to-br from-[var(--brand-gold)] to-[var(--brand-gold-dark)] flex items-center justify-center">
                  <User className="h-6 w-6 text-[var(--brand-gold-foreground)]" />
                </div>

                <Button
                  variant="outline"
                  className="flex-1 h-12 justify-start text-neutral-500 dark:text-neutral-400 font-normal text-base bg-neutral-50 dark:bg-neutral-900 border-neutral-200 dark:border-neutral-700 hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-all duration-200"
                  onClick={() => setIsExpanded(true)}
                >
                  <Plus className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span className="truncate">What&apos;s on your mind, {customerName}?</span>
                </Button>
              </div>

              {/* Quick Action Buttons */}
              <div className="flex flex-wrap justify-between gap-2 mt-3 px-8 md:mt-0">
                <Button
                  variant="ghost"
                  size="sm"
                  className="flex items-center gap-2 text-neutral-600 dark:text-neutral-400 hover:text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10 transition-all duration-200"
                  onClick={() => setIsExpanded(true)}
                >
                  <ImageIcon className="h-4 w-4" />
                  <span className="text-xs">Photo</span>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="flex items-center gap-2 text-neutral-600 dark:text-neutral-400 hover:text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10 transition-all duration-200"
                  onClick={() => setIsExpanded(true)}
                >
                  <MessageSquare className="h-4 w-4" />
                  <span className="text-xs">Post</span>
                </Button>
              </div>
            </motion.div>
          ) : (
            <motion.div
              key="expanded"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="overflow-hidden"
            >
              {/* Header */}
              <div className="flex items-center justify-between p-4 md:p-6 pb-2 border-b border-neutral-200 dark:border-neutral-700">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-br from-[var(--brand-gold)] to-[var(--brand-gold-dark)] flex items-center justify-center">
                    <User className="h-4 w-4 md:h-5 md:w-5 text-[var(--brand-gold-foreground)]" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-sm md:text-base text-neutral-900 dark:text-neutral-100">
                      Create Post
                    </h3>
                    <p className="text-xs text-neutral-500 dark:text-neutral-400">
                      Share an update with your community
                    </p>
                  </div>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCancel}
                  className="h-8 w-8 p-0 hover:bg-neutral-100 dark:hover:bg-neutral-800"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {/* Post form */}
              <div className="p-4 md:p-6 pt-4">
                <CustomerPostForm
                  onSuccess={handlePostSuccess}
                  onCancel={handleCancel}
                  showCard={false}
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </CardContent>
    </Card>
  );
}
