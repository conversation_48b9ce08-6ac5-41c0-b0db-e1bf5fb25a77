import { type NextRequest, NextResponse } from "next/server";
import { updateSession } from "./utils/supabase/middleware";
import { Ratelimit } from "@upstash/ratelimit";
import { Redis } from "@upstash/redis";

// Initialize Redis client for rate limiting only
const redisUrl = process.env.UPSTASH_REDIS_REST_URL;
const redisToken = process.env.UPSTASH_REDIS_REST_TOKEN;

if (!redisUrl || !redisToken) {
  console.error("Upstash Redis URL or Token is not defined in environment variables.");
}

const redis = redisUrl && redisToken ? new Redis({ url: redisUrl, token: redisToken }) : null;

// Initialize Rate Limiter
const maxRequests = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || "10");
const windowSeconds = parseInt(process.env.RATE_LIMIT_WINDOW_SECONDS || "10");

const ratelimit = redis
  ? new Ratelimit({
      redis: redis,
      limiter: Ratelimit.slidingWindow(maxRequests, `${windowSeconds} s`),
      analytics: true,
      prefix: "@upstash/ratelimit/dukancard",
    })
  : null;


export async function middleware(request: NextRequest) {
  // --- Domain and HTTPS Redirect Logic START ---
  const url = request.nextUrl.clone();
  const hostname = url.hostname;
  const protocol = url.protocol;

  // Only apply redirects in production environment and exclude development/testing domains
  const isDevelopmentDomain = hostname.includes('localhost') ||
                              hostname.includes('ngrok.io') ||
                              hostname.includes('ngrok-free.app') ||
                              hostname.includes('127.0.0.1');

  if (process.env.NODE_ENV === 'production' && !isDevelopmentDomain) {
    let shouldRedirect = false;

    // Check for www redirect (www.dukancard.in -> dukancard.in)
    if (hostname.startsWith('www.')) {
      url.hostname = hostname.replace('www.', '');
      shouldRedirect = true;
    }

    // Check for HTTPS redirect (http:// -> https://)
    if (protocol === 'http:') {
      url.protocol = 'https:';
      shouldRedirect = true;
    }

    // Perform redirect if needed
    if (shouldRedirect) {
      return NextResponse.redirect(url.toString(), 301); // Permanent redirect
    }
  }
  // --- Domain and HTTPS Redirect Logic END ---

  // --- Rate Limiting Logic START ---
  // Apply rate limiting to API routes only (skip webhooks)
  if (request.nextUrl.pathname.startsWith("/api/") && !request.nextUrl.pathname.startsWith("/api/webhooks/")) {
    // Skip rate limiting if Redis is not configured
    if (!ratelimit) {
      console.warn("Rate limiting skipped: Redis not configured");
    } else {
      // Get IP address: Check 'x-forwarded-for' header first, then fallback.
      const forwardedFor = request.headers.get('x-forwarded-for');
      // The header can contain multiple IPs (client, proxy1, proxy2). The client IP is usually the first one.
      const ip = forwardedFor ? forwardedFor.split(',')[0].trim() : "127.0.0.1";

      try {
        // Use Upstash rate limiting
        const { success, limit, remaining, reset } = await ratelimit.limit(ip);

        if (!success) {
          // Rate limit exceeded, return 429
          return new NextResponse("Too Many Requests", {
            status: 429,
            headers: {
              "X-RateLimit-Limit": limit.toString(),
              "X-RateLimit-Remaining": remaining.toString(),
              "X-RateLimit-Reset": new Date(reset * 1000).toISOString(),
            },
          });
        }
      } catch (error) {
        console.error("Rate limiting error:", error);
        // If rate limiting fails, allow the request to proceed
      }
    }
  }
  // --- Rate Limiting Logic END ---


  // Proceed with Supabase session update
  return await updateSession(request);
}

export const config = {
  matcher: [
    /*
     * Match all request paths except forhe ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};