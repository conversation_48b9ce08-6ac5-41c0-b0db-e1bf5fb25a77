"use server";

import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
import { revalidatePath } from "next/cache";
import { z } from "zod";
import { unstable_noStore as _noStore } from "next/cache"; // Import noStore
import { onboardingPlans } from "@/lib/PricingPlans";
import { createAdminClient } from "@/utils/supabase/admin"; // Import admin client to bypass RLS
import { checkBusinessSlugAvailability } from "@/lib/utils/slugUtils";
import { IndianMobileSchema } from "@/lib/schemas/authSchemas";
import { cleanPhoneFromAuth } from "@/lib/utils";
import {
  SubscriptionStateManager,
} from "@/lib/razorpay/webhooks/handlers/subscription-state-manager";
import {
  SUBSCRIPTION_STATUS
} from "@/lib/razorpay/webhooks/handlers/subscription-constants";

// Type for existing business profile data
export type ExistingBusinessProfileData = {
  businessName?: string;
  email?: string;
  memberName?: string;
  title?: string;
  phone?: string;
  businessCategory?: string;
  businessSlug?: string;
  addressLine?: string;
  pincode?: string;
  city?: string;
  state?: string;
  locality?: string;
  businessStatus?: "online" | "offline";
  // Subscription data
  planId?: string;
  hasExistingSubscription?: boolean;
};

/**
 * Fetch existing business profile data for the current user
 * This is used to pre-fill the onboarding form if the user already has some data
 */
export async function getExistingBusinessProfileData(): Promise<{
  data?: ExistingBusinessProfileData;
  error?: string;
}> {
  try {
    const supabase = await createClient();

    // Get the authenticated user first
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return { error: "User not authenticated." };
    }

    // Use regular client for auth.users data access

    // Fetch existing business profile data
    const { data: profileData, error: profileError } = await supabase
      .from("business_profiles")
      .select(`
        business_name,
        contact_email,
        member_name,
        title,
        phone,
        business_category,
        business_slug,
        address_line,
        pincode,
        city,
        state,
        locality,
        status
      `)
      .eq("id", user.id)
      .maybeSingle();

    if (profileError) {
      console.error("Error fetching existing business profile:", profileError);
      return { error: "Failed to fetch existing profile data." };
    }

    // If no profile exists, check if we can pre-fill data from auth.users
    if (!profileData) {
      // Get phone, email, and name from auth.users table to pre-fill if available
      let phoneFromAuth = null;
      let emailFromAuth = null;
      let nameFromAuth = null;

      if (user.phone) {
        phoneFromAuth = cleanPhoneFromAuth(user.phone);
      }

      // Get email from auth.users
      if (user.email) {
        emailFromAuth = user.email;
      }

      // Get full name from auth.users metadata (try multiple possible fields)
      if (user.user_metadata?.full_name) {
        nameFromAuth = user.user_metadata.full_name;
      } else if (user.user_metadata?.name) {
        nameFromAuth = user.user_metadata.name;
      } else if (user.user_metadata?.display_name) {
        nameFromAuth = user.user_metadata.display_name;
      }

      const preFilledData: ExistingBusinessProfileData = {};
      if (phoneFromAuth) preFilledData.phone = phoneFromAuth;
      if (emailFromAuth) preFilledData.email = emailFromAuth;
      if (nameFromAuth) preFilledData.memberName = nameFromAuth;

      return {
        data: preFilledData
      };
    }

    // Fetch subscription data to check if user already has a plan
    const { data: subscriptionData, error: subscriptionError } = await supabase
      .from("payment_subscriptions")
      .select("plan_id")
      .eq("business_profile_id", user.id)
      .order("created_at", { ascending: false })
      .limit(1)
      .maybeSingle();

    if (subscriptionError) {
      console.error("Error fetching subscription data:", subscriptionError);
      // Continue without subscription data - don't fail the entire operation
    }

    // Process phone number - ensure it's in 10-digit format without +91 prefix
    let processedPhone = cleanPhoneFromAuth(profileData.phone) || undefined;

    // If no phone in business_profiles, try to get from auth.users
    if (!processedPhone && user.phone) {
      processedPhone = cleanPhoneFromAuth(user.phone) || undefined;
    }

    // Process member name - use business profile data or fallback to auth.users display_name
    let processedMemberName = profileData.member_name || undefined;
    if (!processedMemberName && user.user_metadata?.display_name) {
      processedMemberName = user.user_metadata.display_name;
    }

    // Map database fields to form field names
    const existingData: ExistingBusinessProfileData = {
      businessName: profileData.business_name || undefined,
      email: profileData.contact_email || undefined,
      memberName: processedMemberName,
      title: profileData.title || undefined,
      phone: processedPhone,
      businessCategory: profileData.business_category || undefined,
      businessSlug: profileData.business_slug || undefined,
      addressLine: profileData.address_line || undefined,
      pincode: profileData.pincode || undefined,
      city: profileData.city || undefined,
      state: profileData.state || undefined,
      locality: profileData.locality || undefined,
      businessStatus: (profileData.status === "online" ? "online" : "offline") as "online" | "offline",
      // Subscription data
      planId: subscriptionData?.plan_id || undefined,
      hasExistingSubscription: !!subscriptionData && !!subscriptionData.plan_id,
    };

    return { data: existingData };
  } catch (error) {
    console.error("Unexpected error fetching existing business profile:", error);
    return { error: "An unexpected error occurred." };
  }
}

// Define the schema matching the client-side form (excluding planId which is passed separately)
// Added new fields for multi-step onboarding
const onboardingFormSchema = z.object({
  businessName: z.string().min(2, {
    message: "Business name must be at least 2 characters.",
  }),
  email: z.string().email({ message: "Please enter a valid email." }),
  memberName: z.string().min(2, { message: "Your name is required." }), // Added
  title: z.string().min(2, { message: "Your title/designation is required." }), // Added
  phone: IndianMobileSchema, // Added
  businessCategory: z.string().min(1, { message: "Business category is required." }), // Added
  businessSlug: z
    .string()
    .min(3, { message: "URL slug must be at least 3 characters." })
    .regex(/^[a-z0-9-]+$/, {
      message:
        "URL slug can only contain lowercase letters, numbers, and hyphens.",
    }), // Added
  // Address fields
  addressLine: z.string().min(1, { message: "Address line is required." }),
  pincode: z.string()
    .min(6, { message: "Pincode must be 6 digits." })
    .max(6, { message: "Pincode must be 6 digits." })
    .regex(/^\d+$/, { message: "Pincode must contain only digits." }),
  city: z.string().min(1, { message: "City is required." }),
  state: z.string().min(1, { message: "State is required." }),
  locality: z.string().min(1, { message: "Locality/area is required." }),
  // Business status
  businessStatus: z.enum(["online", "offline"]).default("online"),
});

// Type for the data expected by the action
interface CreateBusinessProfileData {
  formData: FormData;
  planId: string; // Pass planId separately
  redirectSlug?: string | null; // Optional redirect slug for card page
  message?: string | null; // Optional message parameter for redirect
}

export async function createBusinessProfile(data: CreateBusinessProfileData) {
  const { formData, planId, redirectSlug, message: _message } = data;

  // Validate basic inputs
  if (!planId) {
    return { error: "Plan selection is missing." };
  }

  // Ensure planId is for an available plan
  const availablePlanIds = onboardingPlans.filter(plan => plan.available).map(plan => plan.id);
  if (!availablePlanIds.includes(planId)) {
    return { error: "Selected plan is not available. Please choose an active plan." };
  }

  const validatedFields = onboardingFormSchema.safeParse({
    businessName: formData.get("businessName"),
    email: formData.get("email"),
    memberName: formData.get("memberName"), // Added
    title: formData.get("title"), // Added
    phone: formData.get("phone"), // Added
    businessCategory: formData.get("businessCategory"), // Added
    businessSlug: formData.get("businessSlug"), // Added
    addressLine: formData.get("addressLine"), // Added
    pincode: formData.get("pincode"), // Added
    city: formData.get("city"), // Added
    state: formData.get("state"), // Added
    locality: formData.get("locality"), // Added
    businessStatus: formData.get("businessStatus"), // Added
  });

  if (!validatedFields.success) {
    console.error(
      "Server-side validation failed:",
      validatedFields.error.flatten().fieldErrors
    );
    return {
      error: "Invalid form data provided.",
      fieldErrors: validatedFields.error.flatten().fieldErrors,
    };
  }

  const { businessName, email, memberName, title, phone, businessCategory, businessSlug, addressLine, pincode, city, state, locality, businessStatus } =
    validatedFields.data; // Added new fields

  const supabase = await createClient();

  // Get the authenticated user on the server
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    console.error(
      "Error fetching user in createBusinessProfile action:",
      userError
    );
    return { error: "User not authenticated." };
  }

  // Create admin client for operations that need to bypass RLS
  const adminClient = createAdminClient();

  // Note: Phone uniqueness check removed as multiple businesses/customers can share the same number

  // --- Security Critical: Calculate trial end date on the server (only for paid plans) ---
  // For free plan, we don't set a trial end date
  let trialEndDate: Date | null = null;
  if (planId !== "free") {
    // FIXED: Calculate trial end date (30 days from now using milliseconds)
    // This approach avoids month boundary issues that can occur with setDate()
    trialEndDate = new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)); // 30 days in milliseconds
  }

  // Check if profile already exists (safety check)
  const { data: existingProfile, error: profileCheckError } = await supabase
    .from("business_profiles")
    .select("id")
    .eq("id", user.id)
    .maybeSingle();

  if (profileCheckError) {
    console.error("Error checking existing business profile:", profileCheckError);
    return { error: "Database error checking profile." };
  }

  if (existingProfile) {
    // Redirect to the card page if redirectSlug is provided, otherwise to business dashboard
    if (redirectSlug) {
      // Don't pass message parameter back to public card page
      redirect(`/${redirectSlug}`);
    } else {
      // Redirect to dashboard, assuming it's the correct place
      redirect("/dashboard/business");
    }
  }

  // --- Check if slug is already taken using shared utility ---
  const { available, error: slugCheckError } = await checkBusinessSlugAvailability(businessSlug);

  if (slugCheckError) {
    console.error("Error checking existing slug:", slugCheckError);
    return { error: "Database error checking URL slug." };
  }

  if (!available) {
    return {
      error: "This URL slug is already taken. Please choose another.",
      fieldErrors: { businessSlug: ["This URL slug is already taken."] },
    };
  }

  // Prepare the business profile data
  const profileData: {
    id: string;
    business_name: string;
    contact_email: string;
    has_active_subscription: boolean;
    member_name: string;
    title: string;
    phone: string;
    business_slug: string;
    business_category: string;
    address_line: string;
    pincode: string;
    city: string;
    state: string;
    locality: string;
    status: string;
    trial_end_date?: string;
  } = {
    id: user.id,
    business_name: businessName,
    contact_email: email,
    has_active_subscription: SubscriptionStateManager.shouldHaveActiveSubscription(
      planId === "free" ? SUBSCRIPTION_STATUS.ACTIVE : SUBSCRIPTION_STATUS.TRIAL,
      planId
    ), // Use centralized logic
    member_name: memberName,
    title: title,
    phone: phone,
    business_slug: businessSlug,
    business_category: businessCategory,
    address_line: addressLine,
    pincode: pincode,
    city: city,
    state: state,
    locality: locality,
    status: businessStatus,
  };

  // Only add trial_end_date for paid plans
  if (trialEndDate) {
    profileData.trial_end_date = trialEndDate.toISOString();
  }

  // Insert into the database
  const { error: insertError } = await supabase
    .from("business_profiles")
    .insert(profileData);

  if (insertError) {
    console.error("Error creating business profile:", insertError);
    // Handle specific errors like unique constraint if needed
    return { error: "Failed to create business profile." };
  }

  // Note: We no longer update auth.users table from onboarding
  // Contact email is only stored in business_profiles table
  // Phone and other auth fields remain unchanged during onboarding

  // Create a corresponding record in the payment_subscriptions table
  // Using the admin client created earlier

  // Prepare subscription data with appropriate status based on plan
  const subscriptionData: {
    business_profile_id: string;
    plan_id: string;
    plan_cycle: string;
    subscription_status: string;
    created_at: string;
    updated_at: string;
    subscription_start_date?: string;
  } = {
    business_profile_id: user.id,
    plan_id: planId,
    plan_cycle: "monthly", // Always monthly on onboarding
    subscription_status: planId === "free" ? SUBSCRIPTION_STATUS.ACTIVE : SUBSCRIPTION_STATUS.TRIAL, // Free plan is active, paid plans start as trial
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  // For free plan, set subscription dates appropriately
  if (planId === "free") {
    // For free plan, set start date to now but no expiry
    subscriptionData.subscription_start_date = new Date().toISOString();
  }

  const { error: subscriptionError } = await adminClient
    .from("payment_subscriptions")
    .insert(subscriptionData);

  if (subscriptionError) {
    console.error("Error creating payment subscription record:", subscriptionError);
    // Continue anyway, as the business profile was created successfully
    // We don't want to fail the entire onboarding process if this fails
  }

  // Revalidate paths
  revalidatePath("/(onboarding)/onboarding");
  revalidatePath("/dashboard/business");

  // Redirect to the card page if redirectSlug is provided, otherwise to business dashboard
  if (redirectSlug) {
    // Also revalidate the public card page
    revalidatePath(`/${redirectSlug}`);

    // Don't pass message parameter back to public card page
    redirect(`/${redirectSlug}`);
  } else {
    // Redirect to the business dashboard after successful creation
    redirect("/dashboard/business");
  }

  // return { success: true }; // Redirect happens first
}

// --- New Server Action: Check Slug Availability ---
export async function checkSlugAvailability(
  slug: string
): Promise<{ available: boolean }> {
  // Call the shared utility function
  const result = await checkBusinessSlugAvailability(slug);

  // The onboarding interface expects just { available: boolean }
  // So we need to adapt the response from the shared utility
  return { available: result.available };
}
