import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';

/**
 * Plan Prioritizer - Handles business plan-based post prioritization
 * Higher tier businesses get better visibility while maintaining fairness
 */

export const PLAN_PRIORITY: Record<string, number> = {
  'enterprise': 5,
  'pro': 4,
  'growth': 3,
  'basic': 2,
  'free': 1
};

export interface BusinessGroup {
  authorId: string;
  priority: number;
  latestPostTime: number;
  posts: UnifiedPost[];
}

/**
 * Create business priority groups based on subscription plans
 * Similar to how LinkedIn prioritizes premium content
 */
export function createBusinessPriorityGroups(businessPosts: UnifiedPost[]): BusinessGroup[] {
  // Group posts by business author
  const businessPostsByAuthor = new Map<string, UnifiedPost[]>();
  businessPosts.forEach(post => {
    if (!businessPostsByAuthor.has(post.author_id)) {
      businessPostsByAuthor.set(post.author_id, []);
    }
    businessPostsByAuthor.get(post.author_id)!.push(post);
  });

  // Sort posts within each business group chronologically (latest first)
  businessPostsByAuthor.forEach((posts, authorId) => {
    businessPostsByAuthor.set(authorId, posts.sort((a, b) =>
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    ));
  });

  // Create priority groups
  return Array.from(businessPostsByAuthor.entries())
    .map(([authorId, authorPosts]) => {
      const latestPost = authorPosts[0];
      const priority = PLAN_PRIORITY[latestPost.business_plan || 'free'] || 1;
      return {
        authorId,
        priority,
        latestPostTime: new Date(latestPost.created_at).getTime(),
        posts: authorPosts
      };
    })
    .sort((a, b) => {
      // Sort by plan priority first
      if (a.priority !== b.priority) {
        return b.priority - a.priority; // Higher priority first
      }
      // If same plan, sort by latest post timestamp
      return b.latestPostTime - a.latestPostTime;
    });
}

/**
 * Distribute business posts with plan-based prioritization
 * Uses tier-based round-robin to ensure diversity within each plan level
 */
export function distributePrioritizedBusinessPosts(businessGroups: BusinessGroup[]): UnifiedPost[] {
  const result: UnifiedPost[] = [];

  // Group businesses by plan priority
  const businessesByPlan = new Map<number, BusinessGroup[]>();
  businessGroups.forEach(business => {
    if (!businessesByPlan.has(business.priority)) {
      businessesByPlan.set(business.priority, []);
    }
    businessesByPlan.get(business.priority)!.push(business);
  });

  // Sort plan priorities (highest first)
  const sortedPlanPriorities = Array.from(businessesByPlan.keys()).sort((a, b) => b - a);

  // Distribute posts: round-robin within each plan tier
  for (const planPriority of sortedPlanPriorities) {
    const businessesInPlan = businessesByPlan.get(planPriority)!;

    // Create queues for round-robin distribution
    const businessPostQueues = businessesInPlan.map(business => ({
      ...business,
      remainingPosts: [...business.posts]
    }));

    // Round-robin within this plan tier until all posts are distributed
    while (businessPostQueues.some(queue => queue.remainingPosts.length > 0)) {
      businessPostQueues.forEach(business => {
        if (business.remainingPosts.length > 0) {
          const post = business.remainingPosts.shift()!;
          result.push(post);
        }
      });
    }
  }

  return result;
}

/**
 * Get plan display name for UI purposes
 */
export function getPlanDisplayName(planId: string): string {
  const planNames: Record<string, string> = {
    'enterprise': 'Enterprise',
    'pro': 'Pro',
    'growth': 'Growth',
    'basic': 'Basic',
    'free': 'Free'
  };
  
  return planNames[planId] || 'Free';
}

/**
 * Check if a business has premium features based on plan
 */
export function hasPremiumFeatures(planId: string): boolean {
  const priority = PLAN_PRIORITY[planId] || 1;
  return priority >= PLAN_PRIORITY.growth; // Growth and above
}
