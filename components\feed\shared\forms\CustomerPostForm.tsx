'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { motion, AnimatePresence } from 'framer-motion';
import { Send, Plus, Loader2 } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { createCustomerPost, updateCustomerPost } from '@/lib/actions/customerPosts';
import { customerPostSchema, CustomerPostFormData, CustomerPostData } from '@/lib/types/posts';
import MediaUpload from './MediaUpload';
import ImageCropper from './ImageCropper';
import { useCustomerPostMediaUpload } from '../hooks/useCustomerPostMediaUpload';
import { createClient } from '@/utils/supabase/client';
import { useRouter } from 'next/navigation';

interface CustomerPostFormProps {
  post?: CustomerPostData;
  onSuccess?: () => void;
  onCancel?: () => void;
  showCard?: boolean;
}

export default function CustomerPostForm({ post, onSuccess, onCancel, showCard = true }: CustomerPostFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadMethod, setUploadMethod] = useState<'upload' | 'url'>('upload');
  const router = useRouter();

  // Initialize form with existing post data or defaults
  const form = useForm<CustomerPostFormData>({
    resolver: zodResolver(customerPostSchema),
    defaultValues: {
      id: post?.id,
      content: post?.content || '',
      image_url: post?.image_url || '',
      mentioned_business_ids: post?.mentioned_business_ids || [],
    },
  });

  // Media upload hook
  const {
    uploadError,
    previewUrl,
    isUploading,
    imageToCrop,
    originalFile,
    handleFileSelect,
    handleUpload,
    clearImage,
    handleCropComplete,
    handleCropCancel,
  } = useCustomerPostMediaUpload({
    onUploadSuccess: (url) => {
      form.setValue('image_url', url, { shouldDirty: true });
    },
    onUploadError: (error) => {
      toast.error('Upload failed', { description: error });
    },
  });

  // Clear all images (both uploaded and URL)
  const handleClearAllImages = () => {
    clearImage();
    form.setValue('image_url', '', { shouldDirty: true });
  };

  // Check if customer has complete address
  const checkCustomerAddress = async () => {
    const supabase = createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      toast.error('Please log in to continue');
      return false;
    }

    const { data: profile, error } = await supabase
      .from('customer_profiles')
      .select('pincode, city, state, locality')
      .eq('id', user.id)
      .single();

    if (error) {
      toast.error('Failed to check customer profile');
      return false;
    }

    // Check if all address fields are filled
    if (!profile?.pincode || !profile?.city || !profile?.state || !profile?.locality) {
      toast.error('Please complete your customer profile before creating posts', {
        description: 'You will be redirected to update your profile',
        action: {
          label: 'Update Now',
          onClick: () => router.push('/dashboard/customer/profile')
        }
      });

      // Redirect after a short delay
      setTimeout(() => {
        router.push('/dashboard/customer/profile');
      }, 2000);

      return false;
    }

    return true;
  };

  // Handle form submission
  const onSubmit = async (data: CustomerPostFormData) => {
    setIsSubmitting(true);

    try {
      // Check customer address before creating new posts
      if (!post?.id) {
        const hasValidAddress = await checkCustomerAddress();
        if (!hasValidAddress) {
          setIsSubmitting(false);
          return;
        }
      }
      const finalData = { ...data };

      // Clean up image_url field - convert empty strings to null
      if (!finalData.image_url || finalData.image_url.trim() === '') {
        finalData.image_url = null;
      }

      let result;

      if (post?.id) {
        // Updating existing post
        result = await updateCustomerPost(post.id, finalData);
      } else {
        // Creating new post
        if (uploadMethod === 'upload' && previewUrl && !finalData.image_url) {
          // For new posts with uploaded images:
          // 1. Create post first without image_url to get real post ID
          // 2. Upload image using real post ID
          // 3. Update post with image_url

          const createResult = await createCustomerPost({ ...finalData, image_url: null });
          if (!createResult.success || !createResult.data) {
            toast.error(createResult.error || 'Failed to create post');
            return;
          }

          // Type assertion for the created post data
          const createdPost = createResult.data as { id: string; created_at: string };

          // Upload the image using the real post ID and creation date
          const uploadedUrl = await handleUpload(createdPost.id, undefined, createdPost.created_at);
          if (uploadedUrl) {
            // Update the post with the image URL
            const updateResult = await updateCustomerPost(createdPost.id, {
              ...finalData,
              image_url: uploadedUrl
            });
            result = updateResult;
          } else {
            // Image upload failed, but post was created
            result = createResult;
          }
        } else {
          // No image upload needed, create post normally
          result = await createCustomerPost(finalData);
        }
      }

      if (result.success) {
        toast.success(result.message);
        form.reset();
        clearImage();
        if (onSuccess) {
          onSuccess();
        }
      } else {
        toast.error(result.error || result.message);
      }
    } catch (error) {
      console.error('Error submitting post:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const formContent = (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className="space-y-4">
          {/* Post content */}
          <FormField
            control={form.control}
            name="content"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Content</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="What's on your mind?"
                    className="min-h-[120px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Image Upload/URL Section */}
          <FormField
            control={form.control}
            name="image_url"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Add Image (optional)</FormLabel>
                <FormControl>
                  <div className="space-y-4">
                    {/* Upload method tabs */}
                    <Tabs value={uploadMethod} onValueChange={(value) => setUploadMethod(value as 'upload' | 'url')}>
                      <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="upload">Upload Image</TabsTrigger>
                        <TabsTrigger value="url">Image URL</TabsTrigger>
                      </TabsList>

                      <TabsContent value="upload" className="space-y-4">
                        <MediaUpload
                          previewUrl={previewUrl || (uploadMethod === 'upload' ? (field.value || null) : null)}
                          isUploading={isUploading}
                          uploadError={uploadError}
                          onFileSelect={handleFileSelect}
                          onClearImage={handleClearAllImages}
                          disabled={isSubmitting}
                        />
                      </TabsContent>

                      <TabsContent value="url" className="space-y-4">
                        <Input
                          placeholder="https://example.com/image.jpg"
                          {...field}
                          value={uploadMethod === 'url' ? (field.value || '') : ''}
                          onChange={(e) => {
                            field.onChange(e);
                            if (uploadMethod === 'url') {
                              clearImage(); // Clear any uploaded image when switching to URL
                            }
                          }}
                          disabled={isSubmitting}
                        />
                        {uploadMethod === 'url' && field.value && (
                          <div className="relative mt-2 rounded-md overflow-hidden border border-border">
                            <div className="relative w-full aspect-square max-w-md mx-auto">
                              <img
                                src={field.value}
                                alt="Preview"
                                className="w-full h-full object-cover"
                                onError={() => {
                                  toast.error('Failed to load image');
                                  form.setValue('image_url', '');
                                }}
                              />
                            </div>
                          </div>
                        )}
                      </TabsContent>
                    </Tabs>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

        </div>

        <div className="flex flex-col-reverse sm:flex-row gap-3 pt-6">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
              className="flex-1 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200"
            >
              Cancel
            </Button>
          )}

          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="flex-1"
          >
            <Button
              type="submit"
              disabled={isSubmitting}
              className={`
                w-full relative overflow-hidden
                bg-gradient-to-r from-green-500 to-green-600
                hover:from-green-600 hover:to-green-700
                text-white font-medium
                shadow-lg hover:shadow-xl
                transition-all duration-300
                before:absolute before:inset-0
                before:bg-gradient-to-r before:from-green-400 before:to-green-500
                before:opacity-0 hover:before:opacity-20
                before:transition-opacity before:duration-300
                ${isSubmitting ? 'cursor-not-allowed opacity-80' : ''}
              `}
              style={{
                boxShadow: isSubmitting
                  ? '0 4px 20px rgba(34, 197, 94, 0.3)'
                  : '0 4px 20px rgba(34, 197, 94, 0.4), 0 0 20px rgba(34, 197, 94, 0.2)'
              }}
            >
              <AnimatePresence mode="wait">
                {isSubmitting ? (
                  <motion.div
                    key="submitting"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 10 }}
                    className="flex items-center justify-center"
                  >
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    {post?.id ? 'Updating...' : 'Posting...'}
                  </motion.div>
                ) : (
                  <motion.div
                    key="submit"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 10 }}
                    className="flex items-center justify-center"
                  >
                    {post?.id ? (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        Update Post
                      </>
                    ) : (
                      <>
                        <Plus className="h-4 w-4 mr-2" />
                        Create Post
                      </>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </Button>
          </motion.div>
        </div>
      </form>

      {/* Image Cropper Dialog */}
      <ImageCropper
        isOpen={!!imageToCrop}
        imageSrc={imageToCrop || ''}
        onCropComplete={handleCropComplete}
        onCancel={handleCropCancel}
        originalFileName={originalFile?.name}
      />
    </Form>
  );

  if (!showCard) {
    return formContent;
  }

  return (
    <>
      <Card className="w-full">
        <CardHeader>
          <CardTitle>{post?.id ? 'Edit Post' : 'Create New Post'}</CardTitle>
        </CardHeader>
        <CardContent>
          {formContent}
        </CardContent>
      </Card>
    </>
  );
}
