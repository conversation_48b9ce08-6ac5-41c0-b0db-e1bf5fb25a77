/**
 * Main exports for posts actions
 * This file serves as the public API for all posts-related functionality
 */

// CRUD operations
export { createPost, updatePost, updatePostContent, updatePostProducts, deletePost } from './crud';

// Unified feed functionality (business + customer posts)
export { getUnifiedFeedPosts, getUnifiedFeedPostsWithAuthors } from './unifiedFeed';
export type { UnifiedPost, UnifiedFeedResponse } from './unifiedFeed';

// Utility functions (if needed externally)
export { calculatePostScore } from './utils';

// Types (if needed externally)
export type { UserProfile, PostWithScore } from './types';
