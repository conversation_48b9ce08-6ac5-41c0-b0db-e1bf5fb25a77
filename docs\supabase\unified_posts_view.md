# Unified Posts View Documentation

## View Overview

The `unified_posts` view in the Dukancard application combines posts from both `business_posts` and `customer_posts` tables into a single, unified interface. This view enables efficient pagination and chronological ordering of mixed content types in feeds while maintaining the separation of underlying data structures.

**Latest Update**: Enhanced with author information and business plan prioritization for improved feed experience.

## View Purpose

This view solves the pagination challenge when displaying both business and customer posts together in feeds. Instead of fetching from two separate tables and merging in application code (which causes pagination gaps and performance issues), the view provides a single query interface with perfect chronological ordering.

## View Schema

| Column Name | Data Type | Source | Description |
|-------------|-----------|---------|-------------|
| id | uuid | Both tables | Unique identifier for the post |
| post_source | text | Computed | Either 'business' or 'customer' indicating post origin |
| author_id | uuid | Both tables | ID of the post author (business_id or customer_id) |
| content | text | Both tables | The text content of the post (max 500 characters) |
| image_url | text | Both tables | URL to an image associated with the post |
| created_at | timestamptz | Both tables | Timestamp when the post was created |
| updated_at | timestamptz | Both tables | Timestamp when the post was last updated |
| city_slug | text | Both tables | Slug version of the city name (for location filtering) |
| state_slug | text | Both tables | Slug version of the state name (for location filtering) |
| locality_slug | text | Both tables | Slug version of the locality name (for location filtering) |
| pincode | text | Both tables | Pincode/ZIP code (for location filtering) |
| product_ids | uuid[] | Both tables | Product IDs for business posts, mentioned_business_ids for customer posts |
| mentioned_business_ids | uuid[] | Both tables | Business IDs mentioned in the post |
| **author_name** | text | **Profile tables** | **Business name or customer name** |
| **author_avatar** | text | **Profile tables** | **Business logo or customer avatar URL** |
| **business_slug** | text | **Business profiles** | **Business slug for URL generation (NULL for customer posts)** |
| **phone** | text | **Business profiles** | **Business phone number (NULL for customer posts)** |
| **whatsapp_number** | text | **Business profiles** | **Business WhatsApp number (NULL for customer posts)** |
| **business_plan** | text | **Payment subscriptions** | **Business plan (enterprise/pro/growth/free) for prioritization** |

## View Definition

```sql
CREATE VIEW unified_posts AS
SELECT
    bp.id,
    'business'::text as post_source,
    bp.business_id as author_id,
    bp.content,
    bp.image_url,
    bp.created_at,
    bp.updated_at,
    bp.city_slug,
    bp.state_slug,
    bp.locality_slug,
    bp.pincode,
    bp.product_ids,
    bp.mentioned_business_ids,
    bprofile.business_name AS author_name,
    bprofile.logo_url AS author_avatar,
    bprofile.business_slug AS business_slug,
    bprofile.phone AS phone,
    bprofile.whatsapp_number AS whatsapp_number,
    COALESCE(ps.plan_id, 'free') AS business_plan
FROM business_posts bp
LEFT JOIN business_profiles bprofile ON bp.business_id = bprofile.id
LEFT JOIN payment_subscriptions ps ON bprofile.id = ps.business_profile_id

UNION ALL

SELECT
    cp.id,
    'customer'::text as post_source,
    cp.customer_id as author_id,
    cp.content,
    cp.image_url,
    cp.created_at,
    cp.updated_at,
    cp.city_slug,
    cp.state_slug,
    cp.locality_slug,
    cp.pincode,
    cp.mentioned_business_ids as product_ids,
    cp.mentioned_business_ids,
    cprofile.name AS author_name,
    cprofile.avatar_url AS author_avatar,
    NULL AS business_slug,
    NULL AS phone,
    NULL AS whatsapp_number,
    NULL AS business_plan
FROM customer_posts cp
LEFT JOIN customer_profiles cprofile ON cp.customer_id = cprofile.id

ORDER BY created_at DESC;
```

## Security Configuration

This view follows Supabase security best practices:
- **No SECURITY DEFINER**: View uses the permissions of the querying user (not the view creator)
- **Inherited RLS**: Row Level Security policies from source tables are automatically applied
- **User-based access**: Each user sees only the data they're authorized to access

## Row Level Security (RLS)

The view **inherits RLS policies** from the source tables:
- **business_posts**: Existing RLS policies apply
- **customer_posts**: Existing RLS policies apply
- **No additional RLS needed** on the view itself

When querying the view, PostgreSQL automatically applies the appropriate RLS policies from both source tables, ensuring users only see posts they're authorized to view.

## Business Plan Prioritization

### Overview
The unified feed implements business plan prioritization to give higher visibility to premium businesses while maintaining fair exposure for all users.

### Priority Order
1. **Enterprise** - Highest priority
2. **Pro** - High priority
3. **Growth** - Medium-high priority
4. **Basic** - Medium priority
5. **Free** - Standard priority

### Implementation
- **Customer posts**: Unaffected by prioritization, maintain chronological order
- **Business diversity**: Shows latest post per business to prevent feed domination
- **Plan prioritization**: Higher plan businesses get better visibility
- **Smart pagination**: Fetches 3x posts for smart feed, applies prioritization, returns target count
- **Hybrid approach**: Database-level pagination + client-side smart filtering

### Smart Feed Algorithm

The feed prioritization algorithm ensures higher-plan businesses get better visibility while showing all posts from all businesses.

**Key Features:**
- **All posts shown**: Every post from every business appears in the feed
- **Business prioritization**: Higher-plan businesses' posts appear earlier
- **Content diversity**: Prevents single business from dominating feed
- **Customer posts unaffected**: Maintain chronological order

**Algorithm Overview:**
1. Separate customer and business posts
2. Group business posts by business (author_id)
3. Sort posts within each business chronologically
4. Order business groups by plan priority and latest post timestamp
5. Flatten business posts in priority order
6. Merge with customer posts chronologically

**Example Result:**
```
- Customer Post A (latest)
- Enterprise Business X - Post 1 (latest from X)
- Enterprise Business X - Post 2 (older from X)
- Pro Business Y - Post 1 (latest from Y)
- Customer Post B
- Pro Business Y - Post 2 (older from Y)
- Growth Business Z - Post 1
- Free Business W - Post 1
```

**Detailed Documentation**: See [Feed Prioritization Algorithm](../algorithms/feed_prioritization.md) for complete technical details, examples, and configuration options.

### Business Information Integration

The view now includes comprehensive business information for interactive feed features:

**Business Profile Data:**
- **business_slug**: For clickable business names linking to profiles using `/business/{business_slug}`
- **phone**: For "Call Now" buttons in feed posts
- **whatsapp_number**: For "WhatsApp" buttons in feed posts
- **Automatic Updates**: All business profile changes are immediately reflected in the view
- **Customer Posts**: All business fields are NULL for customer posts (no business profile to link to)

**Key Features:**
- **URL Generation**: Business names can link directly to business profiles
- **Contact Actions**: Phone and WhatsApp buttons are functional with real contact data
- **Unique Identification**: Each business has a unique slug for reliable URL routing
- **SEO Friendly**: Human-readable URLs for better search engine optimization

**Usage Examples:**
```typescript
// Generate clickable business name link
const businessLink = post.business_slug
  ? `/business/${post.business_slug}`
  : null;

// Render business name as clickable or plain text
{post.post_source === 'business' && post.business_slug ? (
  <Link href={`/business/${post.business_slug}`}>
    {post.author_name}
  </Link>
) : (
  <span>{post.author_name}</span>
)}

// Enable contact buttons based on available contact information
const hasPhone = post.phone && post.phone.trim() !== '';
const hasWhatsApp = post.whatsapp_number && post.whatsapp_number.trim() !== '';

// Render contact action buttons
{post.post_source === 'business' && (
  <div className="contact-actions">
    {hasPhone && (
      <Button onClick={() => window.open(`tel:${post.phone}`)}>
        Call Now
      </Button>
    )}
    {hasWhatsApp && (
      <Button onClick={() => window.open(`https://wa.me/${post.whatsapp_number}`)}>
        WhatsApp
      </Button>
    )}
  </div>
)}
```

### Pagination Strategy
- **Smart Feed**: Fetches 3x posts, applies algorithm, returns target count
- **Other Feeds**: Standard database pagination (no client-side filtering)
- **Performance**: Minimal impact, only applied to smart feed
- **Consistency**: Maintains proper pagination across pages

## Client Architecture

### Supabase Client Usage
- **Client-side queries**: Uses normal Supabase client (`createClient()`) for all feed operations
- **No server load**: Queries run directly from browser to Supabase, reducing server load
- **RLS compliance**: All queries respect Row Level Security policies
- **Real-time capable**: Can be extended with real-time subscriptions

### Author Information Strategy
- **Embedded in view**: Author names, avatars, and business slugs included directly in unified_posts
- **No separate queries**: Eliminates need for admin client or additional API calls
- **Automatic updates**: View reflects profile changes immediately
- **Performance optimized**: Single query fetches all required data
- **Clickable business names**: Business slug enables direct navigation to business profiles

### Automatic Data Synchronization
- **Real-time updates**: View automatically reflects changes in underlying tables
- **Profile changes**: Business name/logo updates appear immediately in feed
- **Plan changes**: Business plan updates reflected instantly in prioritization
- **No manual refresh**: PostgreSQL views are always up-to-date with source data
- **Zero maintenance**: No triggers or manual synchronization required

### Benefits of Client-side Architecture
1. **Reduced server load**: Direct browser-to-database queries
2. **Better performance**: Fewer network hops and server processing
3. **Simplified codebase**: No complex server-side data fetching logic
4. **Scalability**: Database handles query optimization and caching

## Performance Considerations

### Indexes Used
The view leverages existing indexes from source tables:
- **business_posts**: All existing indexes (business_id, created_at, location fields, etc.)
- **customer_posts**: All existing indexes (customer_id, created_at, location fields, etc.)

### Query Optimization
- **UNION ALL**: Used instead of UNION for better performance (no deduplication needed)
- **WHERE clauses**: Applied at source table level for optimal filtering
- **ORDER BY**: Applied after union for proper chronological ordering

### Scalability
- **Current Scale**: Efficient up to ~100M total posts
- **Future Scale**: Can be replaced with Elasticsearch/OpenSearch for billions+ posts
- **Migration Path**: API interface remains the same when switching to search engine

## Usage Patterns

### Feed Queries
```sql
-- Smart feed with complete business information for clickable actions
SELECT id, post_source, author_id, content, image_url, created_at,
       author_name, author_avatar, business_slug, phone, whatsapp_number, business_plan
FROM unified_posts
WHERE (post_source = 'business' AND author_id IN (...subscribed_businesses...))
   OR (locality_slug = 'user_locality')
   OR (post_source = 'customer' AND author_id = 'user_id')
ORDER BY created_at DESC
LIMIT 10 OFFSET 20;

-- Location-based feed with contact information for call/WhatsApp buttons
SELECT id, post_source, author_name, author_avatar, business_slug, phone, whatsapp_number, content, business_plan
FROM unified_posts
WHERE city_slug = 'rourkela'
ORDER BY created_at DESC
LIMIT 10;

-- Premium business posts prioritization with business slug for clickable names
SELECT id, post_source, author_name, business_slug, content, business_plan
FROM unified_posts
WHERE post_source = 'business' AND business_plan IN ('enterprise', 'pro')
ORDER BY
  CASE business_plan
    WHEN 'enterprise' THEN 4
    WHEN 'pro' THEN 3
    WHEN 'growth' THEN 2
    ELSE 1
  END DESC,
  created_at DESC
LIMIT 10;
```

### Analytics Queries
```sql
-- Post distribution by source and business plan
SELECT post_source, business_plan, COUNT(*) as post_count
FROM unified_posts
GROUP BY post_source, business_plan
ORDER BY post_source, business_plan;

-- Recent activity by location with author info
SELECT city_slug, COUNT(*) as recent_posts,
       COUNT(DISTINCT author_name) as unique_authors
FROM unified_posts
WHERE created_at > NOW() - INTERVAL '7 days'
GROUP BY city_slug
ORDER BY recent_posts DESC;

-- Business plan distribution
SELECT business_plan, COUNT(*) as business_count
FROM unified_posts
WHERE post_source = 'business' AND business_plan IS NOT NULL
GROUP BY business_plan
ORDER BY
  CASE business_plan
    WHEN 'enterprise' THEN 4
    WHEN 'pro' THEN 3
    WHEN 'growth' THEN 2
    ELSE 1
  END DESC;
```

## Benefits

### 1. Perfect Pagination
- **No gaps**: Chronological ordering across all post types
- **No duplicates**: Single query eliminates merge complexity
- **Consistent performance**: Database-optimized sorting and limiting

### 2. Simplified Application Logic
- **Single API endpoint**: One function handles all feed types
- **Consistent interface**: Same query patterns for all filters
- **Reduced complexity**: No application-level merging required
- **No admin client needed**: Author info embedded in view

### 3. Enhanced User Experience
- **Author information**: Names and avatars included directly
- **Business prioritization**: Premium businesses get better visibility
- **Real-time updates**: Profile changes reflected immediately
- **Consistent display**: Unified rendering across post types

### 4. Scalability
- **Database optimized**: Leverages PostgreSQL's query optimization
- **Index utilization**: Uses existing table indexes efficiently
- **Client-side queries**: Reduces server load
- **Future-ready**: Easy migration path to search engines

### 5. Maintainability
- **Automatic updates**: View stays in sync with source tables
- **Schema consistency**: Unified interface for mixed content
- **Type safety**: Clear post_source field for application logic
- **Single source of truth**: All feed data in one view

## Related Tables

### Source Tables
- **business_posts**: Business-generated content with product linking
- **customer_posts**: Customer-generated content with social features

### Referenced Tables (via JOINs in view)
- **business_profiles**: Author information for business posts (business_name, logo_url, business_slug, phone, whatsapp_number)
- **customer_profiles**: Author information for customer posts (name, avatar_url)
- **payment_subscriptions**: Business plan information for prioritization (plan_id)

## Migration Strategy

### Phase 1: Database View (Current)
- Use unified_posts view for all feed queries
- Maintain existing table structures
- Perfect pagination with good performance

### Phase 2: Search Engine (Future)
- Index unified posts in Elasticsearch/OpenSearch
- Maintain view as fallback
- Enhanced search capabilities and massive scale

### Phase 3: Advanced Features (Future)
- Real-time updates via change streams
- ML-powered ranking and personalization
- Advanced analytics and insights

## Monitoring and Maintenance

### Performance Monitoring
- Monitor query execution times on the view
- Track index usage on source tables
- Watch for slow queries during peak usage

### Maintenance Tasks
- **Regular ANALYZE**: Keep statistics updated on source tables
- **Index maintenance**: Monitor and optimize source table indexes
- **View refresh**: Automatic (no manual refresh needed for regular views)

## Best Practices

### Query Optimization
1. **Always include ORDER BY**: Ensure consistent ordering
2. **Use appropriate LIMIT**: Prevent large result sets
3. **Filter early**: Apply WHERE clauses for better performance
4. **Index-friendly filters**: Use indexed columns in WHERE clauses

### Application Integration
1. **Use post_source field**: Determine rendering logic
2. **Handle author_info separately**: Join with profile tables as needed
3. **Implement proper error handling**: Handle view query failures gracefully
4. **Cache when appropriate**: Cache frequently accessed data

This view provides the foundation for scalable, mixed-content feeds while maintaining clean separation of business and customer post data.
