'use client';

import Link from 'next/link';
import Image from 'next/image';
import { formatDistanceToNow } from 'date-fns';
import { motion } from 'framer-motion';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { PostWithBusinessProfile, ProductData } from '@/lib/types/posts';
import PostActions from './PostActions';
import ProductListItem from '@/app/components/ProductListItem';
import { fetchProductsByIds } from '@/lib/actions/products/fetchProductsByIds';
import { Loader2 } from 'lucide-react';
import { MapPin, MoreVertical, Edit3, Trash2, Package } from 'lucide-react';
import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { fetchPostAddress, formatAddressString, formatAddressFromSlugs, PostAddress } from '@/lib/utils/addressUtils';
import { usePostOwnership } from '@/components/feed/shared/hooks/usePostOwnership';
import InlinePostEditor from '@/components/feed/shared/editors/InlinePostEditor';
import PostDeleteDialog from '@/components/feed/shared/dialogs/PostDeleteDialog';
import InlineProductEditor from './InlineProductEditor';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';

interface ModernPostCardProps {
  post: PostWithBusinessProfile;
  index?: number;
  onPostUpdate?: (_postId: string, _newContent: string) => void;
  onPostDelete?: (_postId: string) => void;
  onProductsUpdate?: (_postId: string, _newProductIds: string[]) => void;
}

export default function ModernPostCard({
  post,
  index = 0,
  onPostUpdate,
  onPostDelete,
  onProductsUpdate
}: ModernPostCardProps) {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const [address, setAddress] = useState<PostAddress | null>(null);
  const [addressLoading, setAddressLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [isEditingProducts, setIsEditingProducts] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [currentContent, setCurrentContent] = useState(post.content);
  const [currentProductIds, setCurrentProductIds] = useState<string[]>(post.product_ids || []);
  const [linkedProducts, setLinkedProducts] = useState<ProductData[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(false);

  // Check if current user owns this post
  const { isOwner, isLoading: ownershipLoading } = usePostOwnership({
    postBusinessId: post.business_id
  });

  // Get business profile data
  const business = post.business_profiles;

  // Handle edit save
  const handleEditSave = (newContent: string) => {
    setIsEditing(false);
    setCurrentContent(newContent);
    onPostUpdate?.(post.id, newContent);
  };

  // Handle edit cancel
  const handleEditCancel = () => {
    setIsEditing(false);
  };

  // Handle delete success
  const handleDeleteSuccess = () => {
    setShowDeleteDialog(false);
    onPostDelete?.(post.id);
  };

  // Handle product edit save
  const handleProductEditSave = (newProductIds: string[]) => {
    setIsEditingProducts(false);
    setCurrentProductIds(newProductIds);
    onProductsUpdate?.(post.id, newProductIds);
  };

  // Handle product edit cancel
  const handleProductEditCancel = () => {
    setIsEditingProducts(false);
  };

  // Fetch linked products
  useEffect(() => {
    if (currentProductIds.length === 0) {
      setLinkedProducts([]);
      setIsLoadingProducts(false);
      return;
    }

    const fetchProducts = async () => {
      setIsLoadingProducts(true);
      try {
        const result = await fetchProductsByIds(currentProductIds);

        if (!result.success) {
          console.error('Error fetching products:', result.error);
          setLinkedProducts([]);
          return;
        }

        // Maintain the order of products as specified in currentProductIds array
        const orderedProducts = currentProductIds
          .map(id => result.data?.find(product => product.id === id))
          .filter(Boolean) as ProductData[];

        setLinkedProducts(orderedProducts);
      } catch (err) {
        console.error('Error fetching products:', err);
        setLinkedProducts([]);
      } finally {
        setIsLoadingProducts(false);
      }
    };

    fetchProducts();
  }, [currentProductIds]);

  // Fetch real address data - moved before conditional return
  useEffect(() => {
    if (!business) return; // Early return if no business data

    const loadAddress = async () => {
      setAddressLoading(true);
      try {
        const addressData = await fetchPostAddress(
          post.locality_slug,
          post.city_slug,
          post.state_slug,
          post.pincode
        );
        setAddress(addressData);
      } catch (error) {
        console.error('Error fetching address:', error);
        setAddress(null);
      } finally {
        setAddressLoading(false);
      }
    };

    loadAddress();
  }, [business, post.locality_slug, post.city_slug, post.state_slug, post.pincode]);

  if (!business) {
    return null; // Skip rendering if business profile is missing
  }

  // Format the post date
  const formattedDate = formatDistanceToNow(new Date(post.created_at), { addSuffix: true });

  // Get business initials for avatar fallback
  const getInitials = (name: string | null) => {
    if (!name) return 'B';
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const businessInitials = getInitials(business.business_name);

  // Format address display
  const getDisplayAddress = () => {
    if (addressLoading) {
      return 'Loading address...';
    }

    if (address) {
      return formatAddressString(address);
    }

    // Fallback to slug-based formatting
    return formatAddressFromSlugs(
      post.locality_slug,
      post.city_slug,
      post.state_slug,
      post.pincode
    );
  };

  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        delay: index * 0.1,
        ease: "easeOut"
      }
    }
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      whileHover={{
        y: -2,
        transition: { duration: 0.2, ease: "easeOut" }
      }}
      className={cn(
        "bg-white dark:bg-black rounded-xl border border-neutral-200 dark:border-neutral-800",
        "shadow-sm hover:shadow-md transition-all duration-300",
        "overflow-hidden mb-4 md:mb-6"
      )}
    >
      {/* Post Header */}
      <div className="p-4 pb-2">
        {/* Business Info and Time */}
        <div className="flex items-center justify-between mb-3">
          {business.business_slug ? (
            <Link
              href={`/${business.business_slug}`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center space-x-3 flex-1 min-w-0 group"
            >
              <Avatar className="h-12 w-12 border-2 border-neutral-100 dark:border-neutral-800 transition-transform group-hover:scale-105">
                <AvatarImage
                  src={business.logo_url || ''}
                  alt={business.business_name || 'Business'}
                  className="object-cover"
                />
                <AvatarFallback className="bg-gradient-to-br from-[var(--brand-gold)] to-[var(--brand-gold-dark)] text-[var(--brand-gold-foreground)] font-semibold">
                  {businessInitials}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-base text-neutral-900 dark:text-neutral-100 truncate group-hover:text-[var(--brand-gold)] transition-colors">
                  {business.business_name}
                </h3>
                <div className="text-xs text-neutral-400 dark:text-neutral-500 mt-1">
                  {formattedDate}
                </div>
              </div>
            </Link>
          ) : (
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              <Avatar className="h-12 w-12 border-2 border-neutral-100 dark:border-neutral-800">
                <AvatarImage
                  src={business.logo_url || ''}
                  alt={business.business_name || 'Customer'}
                  className="object-cover"
                />
                <AvatarFallback className="bg-gradient-to-br from-[var(--brand-gold)] to-[var(--brand-gold-dark)] text-[var(--brand-gold-foreground)] font-semibold">
                  {businessInitials}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-base text-neutral-900 dark:text-neutral-100 truncate">
                  {business.business_name}
                </h3>
                <div className="text-xs text-neutral-400 dark:text-neutral-500 mt-1">
                  {formattedDate}
                </div>
              </div>
            </div>
          )}
          {/* Three-dot menu - only show for post owners */}
          {isOwner && !ownershipLoading && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-full"
                >
                  <MoreVertical className="h-5 w-5 text-neutral-500 dark:text-neutral-400" />
                  <span className="sr-only">Open post menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem
                  onClick={() => setIsEditing(true)}
                  className="cursor-pointer"
                >
                  <Edit3 className="h-4 w-4 mr-2" />
                  Edit post
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => setIsEditingProducts(true)}
                  className="cursor-pointer"
                >
                  <Package className="h-4 w-4 mr-2" />
                  Edit products
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => setShowDeleteDialog(true)}
                  className="text-destructive focus:text-destructive cursor-pointer"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete post
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>

        {/* Address */}
        <div className="flex items-center text-sm text-neutral-500 dark:text-neutral-400">
          <MapPin className="h-3.5 w-3.5 mr-1 flex-shrink-0" />
          <span>{getDisplayAddress()}</span>
        </div>
      </div>

      {/* Post Content */}
      <div className="px-4 pb-3">
        {isEditing ? (
          <InlinePostEditor
            postId={post.id}
            initialContent={currentContent}
            onSave={handleEditSave}
            onCancel={handleEditCancel}
          />
        ) : (
          <p className="text-neutral-900 dark:text-neutral-100 text-sm leading-relaxed whitespace-pre-line">
            {currentContent}
          </p>
        )}
      </div>

      {/* Post Image */}
      {post.image_url && (
        <div className="relative w-full">
          <div className="relative w-full aspect-[4/3] bg-neutral-100 dark:bg-neutral-800">
            <Image
              src={post.image_url}
              alt="Post image"
              fill
              className={cn(
                "object-cover transition-all duration-300",
                imageLoading && "blur-sm scale-105",
                imageError && "hidden"
              )}
              onLoad={() => setImageLoading(false)}
              onError={() => {
                setImageError(true);
                setImageLoading(false);
              }}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              priority={index < 3}
            />
            {imageLoading && (
              <div className="absolute inset-0 bg-neutral-200 dark:bg-neutral-700 animate-pulse" />
            )}
          </div>
        </div>
      )}

      {/* Linked Products */}
      {(currentProductIds.length > 0 || isEditingProducts) && (
        <div className="px-4 pt-4">
          {isEditingProducts ? (
            <InlineProductEditor
              postId={post.id}
              initialProductIds={currentProductIds}
              onSave={handleProductEditSave}
              onCancel={handleProductEditCancel}
            />
          ) : (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <div className="p-1.5 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/20 rounded-lg">
                  <Package className="h-4 w-4 text-[var(--brand-gold)]" />
                </div>
                <h4 className="text-sm font-semibold text-neutral-900 dark:text-neutral-100">
                  Featured Products ({linkedProducts.length})
                </h4>
              </div>

              {isLoadingProducts ? (
                <div className="flex justify-center py-8">
                  <div className="flex flex-col items-center gap-2">
                    <Loader2 className="h-6 w-6 animate-spin text-[var(--brand-gold)]" />
                    <span className="text-xs text-muted-foreground">Loading products...</span>
                  </div>
                </div>
              ) : (
                <motion.div
                  className="grid grid-cols-2 lg:grid-cols-3 gap-2 sm:gap-3"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  {linkedProducts.map((product) => (
                    <motion.div
                      key={product.id}
                      className="h-full"
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.2 }}
                    >
                      {business.business_slug ? (
                        <Link
                          href={`/${business.business_slug}/product/${product.slug || product.id}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="block h-full"
                        >
                          <ProductListItem
                            product={{
                              ...product,
                              // Convert to ProductServiceData format
                              description: undefined,
                              product_type: 'physical' as const,
                              base_price: product.base_price || 0,
                              slug: product.slug || undefined,
                              is_available: true,
                              images: product.image_url ? [product.image_url] : undefined,
                              featured_image_index: 0,
                              created_at: new Date(),
                              updated_at: new Date(),
                            }}
                            isLink={false}
                          />
                        </Link>
                      ) : (
                        <ProductListItem
                          product={{
                            ...product,
                            // Convert to ProductServiceData format
                            description: undefined,
                            product_type: 'physical' as const,
                            base_price: product.base_price || 0,
                            slug: product.slug || undefined,
                            is_available: true,
                            images: product.image_url ? [product.image_url] : undefined,
                            featured_image_index: 0,
                            created_at: new Date(),
                            updated_at: new Date(),
                          }}
                          isLink={false}
                        />
                      )}
                    </motion.div>
                  ))}
                </motion.div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Post Actions */}
      <div className="p-4 pt-3">
        <PostActions
          business={business}
          hasWhatsApp={!!(business.whatsapp_number && business.whatsapp_number.trim() !== '')}
          hasPhone={!!(business.phone && business.phone.trim() !== '')}
        />
      </div>

      {/* Delete Dialog */}
      <PostDeleteDialog
        isOpen={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        postId={post.id}
        postContent={currentContent}
        onDeleteSuccess={handleDeleteSuccess}
      />
    </motion.div>
  );
}
