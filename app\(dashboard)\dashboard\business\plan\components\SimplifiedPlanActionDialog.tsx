"use client";
import { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogTitle,
  DialogHeader,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Loader2,
  CreditCard,
  CheckCircle,
  Sparkles,
  Shield,
  Store,
  AlertTriangle
} from "lucide-react";
import { PricingPlan } from "@/lib/PricingPlans";
import { cn } from "@/lib/utils";
import { useSubscriptionProcessing } from "../context/SubscriptionProcessingContext";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import DialogBackground from "./DialogBackground";

// Animation variants
const dialogVariants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.3,
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 10 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 24,
    },
  },
};

const buttonVariants = {
  hover: { scale: 1.03 },
  tap: { scale: 0.98 },
};

// Helper function to get the correct icon based on plan name
const getPlanIcon = (planName: string) => {
  const iconProps = { className: "w-6 h-6 text-[var(--brand-gold)]" };

  if (planName.includes("Basic")) {
    return <CreditCard {...iconProps} />;
  } else if (planName.includes("Growth")) {
    return <Store {...iconProps} />;
  } else if (planName.includes("Pro")) {
    return <Shield {...iconProps} />;
  } else {
    return <Sparkles {...iconProps} />;
  }
};

interface PaymentMethodInfo {
  paymentMethod: string;
  displayName: string;
  requiresNewSubscription: boolean;
  canUpdateSubscription: boolean;
  warningMessage: string | null;
  source?: string;
}

interface SimplifiedPlanActionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  plan: PricingPlan;
  trialEndDate: string | null;
  _onSubscribe: () => Promise<void>;
  isLoading?: boolean; // Optional prop to control loading state from parent
}

export default function SimplifiedPlanActionDialog({
  isOpen,
  onClose,
  plan,
  _onSubscribe,
  isLoading: externalLoading,
}: SimplifiedPlanActionDialogProps) {
  // Get the resetProcessing function from the subscription processing context
  const { resetProcessing } = useSubscriptionProcessing();

  // Ref for checking if component is mounted
  const isMounted = useRef(true);

  // State for client-side rendering check
  const [isClient, setIsClient] = useState(false);

  // State to track if Razorpay modal is open
  const [isRazorpayOpen, setIsRazorpayOpen] = useState(false);

  // Use internal loading state only if external loading is not provided
  const [internalLoading, setInternalLoading] = useState(false);

  // State for payment method information
  const [paymentMethodInfo, setPaymentMethodInfo] = useState<PaymentMethodInfo | null>(null);
  const [isCheckingPaymentMethod, setIsCheckingPaymentMethod] = useState(false);

  // Ref to track if payment method check has been initiated for this dialog session
  const paymentMethodChecked = useRef(false);

  // Add debouncing state to prevent multiple rapid clicks
  const [isProcessing, setIsProcessing] = useState(false);

  // Use external loading state if provided, otherwise use internal state
  // If Razorpay modal is open, we don't want to show loading state
  // Also consider processing state for button disable
  const isLoading = isRazorpayOpen ? false : (externalLoading !== undefined ? externalLoading : internalLoading);
  const isButtonDisabled = isLoading || isProcessing;

  // Set client-side rendering flag
  useEffect(() => {
    setIsClient(true);
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Reset processing state when dialog is closed or unmounted
  useEffect(() => {
    // Only run this effect when the dialog is closed
    if (!isOpen) {
      // Reset internal loading state
      if (externalLoading === undefined) {
        setInternalLoading(false);
      }

      // Reset Razorpay open state
      setIsRazorpayOpen(false);

      // Reset payment method info
      setPaymentMethodInfo(null);
      setIsCheckingPaymentMethod(false);
      paymentMethodChecked.current = false;

      // Reset processing state to ensure no lingering toast notifications
      resetProcessing();
    }
  }, [isOpen, externalLoading, resetProcessing]);

  // Listen for Razorpay modal opening
  useEffect(() => {
    // Function to detect Razorpay modal
    const checkForRazorpayModal = () => {
      // Razorpay creates an iframe with specific attributes
      const razorpayFrame = document.querySelector('iframe[name^="rzp_"]');
      const razorpayBackdrop = document.querySelector('.razorpay-backdrop');
      const razorpayContainer = document.querySelector('.razorpay-container');
      const razorpayModal = document.querySelector('.razorpay-modal');

      const isRazorpayVisible = !!(razorpayFrame || razorpayBackdrop || razorpayContainer || razorpayModal);

      if (isRazorpayVisible !== isRazorpayOpen) {
        console.log('[DIALOG] Razorpay modal state changed:', isRazorpayVisible);
        setIsRazorpayOpen(isRazorpayVisible);
      }
    };

    // Set up an interval to check for Razorpay modal
    const intervalId = setInterval(checkForRazorpayModal, 300); // Check more frequently

    // Clean up interval on unmount
    return () => {
      clearInterval(intervalId);
    };
  }, [isRazorpayOpen]);

  // Effect to check payment method when dialog opens
  useEffect(() => {
    if (isOpen && isClient && !paymentMethodChecked.current && !isCheckingPaymentMethod) {
      paymentMethodChecked.current = true;

      const checkPaymentMethod = async () => {
        setIsCheckingPaymentMethod(true);
        try {
          const response = await fetch('/api/subscriptions/check-payment-method');
          if (response.ok) {
            const result = await response.json();
            if (result.success && result.data) {
              setPaymentMethodInfo(result.data);
            }
          }
        } catch (error) {
          console.error('Error checking payment method:', error);
        } finally {
          setIsCheckingPaymentMethod(false);
        }
      };

      checkPaymentMethod();
    }
  }, [isOpen, isClient, isCheckingPaymentMethod]);

  const handleSubscribe = async () => {
    // Prevent multiple simultaneous subscription attempts
    if (isProcessing || (externalLoading !== undefined && externalLoading)) {
      console.log('[DIALOG] Already processing subscription, ignoring duplicate click');
      return;
    }

    try {
      // Set processing state to prevent duplicate clicks
      setIsProcessing(true);

      // Only set internal loading if we're not using external loading
      if (externalLoading === undefined) {
        setInternalLoading(true);
      }

      // Reset Razorpay open state before starting
      setIsRazorpayOpen(false);

      console.log('[DIALOG] Starting subscription process');

      // Call the actual subscription function
      await _onSubscribe();
      // Dialog will be closed by the parent component
    } catch (_error) {
      console.error('[DIALOG] Subscription error:', _error);

      // Keep the dialog open if there's an error
      // Only reset internal loading if we're not using external loading
      if (externalLoading === undefined && isMounted.current) {
        setInternalLoading(false);
      }
      // Also reset Razorpay open state in case of error
      setIsRazorpayOpen(false);
    } finally {
      // Always reset processing state
      setIsProcessing(false);
    }
  };

  // No need to extract features as per user request

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          // Reset internal loading state when dialog is closed
          if (externalLoading === undefined) {
            setInternalLoading(false);
          }
          // Reset processing state to ensure no lingering toast notifications
          resetProcessing();
          onClose();
        }
      }}
    >
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto w-[95vw] p-0 border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-xl rounded-xl">
        <motion.div
          initial="hidden"
          animate="visible"
          variants={dialogVariants}
          className="relative overflow-hidden"
        >
          {/* Background gradient effect */}
          {isClient && <DialogBackground variant="gold" intensity="medium" />}

          {/* Header with plan icon and name */}
          <DialogHeader className="p-6 pb-4 border-b border-neutral-200 dark:border-neutral-800">
            <motion.div
              variants={itemVariants}
              className="flex items-center gap-3"
            >
              <div className="p-2 rounded-full bg-[var(--brand-gold)]/15">
                {getPlanIcon(plan.name)}
              </div>
              <div>
                <DialogTitle className="text-xl font-semibold text-foreground">
                  {plan.name}
                </DialogTitle>
                {plan.recommended && (
                  <Badge variant="outline" className="mt-1 text-xs bg-[var(--brand-gold)]/10 text-[var(--brand-gold)] border-[var(--brand-gold)]/20">
                    Recommended
                  </Badge>
                )}
              </div>
            </motion.div>
          </DialogHeader>

          {/* Content section */}
          <div className="p-6 space-y-6">
            {/* Price section with animation */}
            <motion.div variants={itemVariants} className="space-y-2">
              <div className="flex items-baseline gap-2">
                <span className="text-3xl font-bold text-foreground">
                  {plan.price}
                </span>
                <span className="text-muted-foreground text-sm">
                  {plan.period}
                </span>
              </div>

              {plan.savings && (
                <div className="text-green-600 dark:text-green-400 text-sm font-medium flex items-center gap-1.5">
                  <CheckCircle className="w-4 h-4" />
                  {plan.savings}
                </div>
              )}
            </motion.div>

            {/* Payment method warning/note with animation */}
            <motion.div variants={itemVariants}>
              {isCheckingPaymentMethod ? (
                <div className="text-xs text-muted-foreground p-3 bg-neutral-100 dark:bg-neutral-900 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Loader2 className="w-3 h-3 animate-spin" />
                    <span>Checking payment method...</span>
                  </div>
                </div>
              ) : paymentMethodInfo?.requiresNewSubscription ? (
                <Alert variant="default" className="bg-amber-50 text-amber-800 border-amber-200 dark:bg-amber-950 dark:text-amber-200 dark:border-amber-800">
                  <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                  <AlertTitle className="text-amber-800 dark:text-amber-200 font-medium">Payment Method Limitation</AlertTitle>
                  <AlertDescription className="text-amber-700 dark:text-amber-300">
                    {paymentMethodInfo.warningMessage}
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="text-xs text-muted-foreground p-3 bg-neutral-100 dark:bg-neutral-900 rounded-lg">
                  <p className="mb-1 font-medium">Payment Method Note:</p>
                  <p>Card payments recommended for easier plan changes. UPI/Netbanking may require cancellation for upgrades.</p>
                </div>
              )}
            </motion.div>
          </div>

          {/* Footer with buttons */}
          <motion.div variants={itemVariants}>
            <DialogFooter className="flex flex-col-reverse sm:flex-row gap-3 p-6 pt-4 border-t border-neutral-200 dark:border-neutral-800 bg-neutral-50 dark:bg-neutral-950">
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  variant="outline"
                  onClick={() => {
                    if (externalLoading === undefined) {
                      setInternalLoading(false);
                    }
                    // Reset processing state to ensure no lingering toast notifications
                    resetProcessing();
                    onClose();
                  }}
                  className="w-full sm:w-auto py-6 rounded-xl transition-all duration-200"
                >
                  Cancel
                </Button>
              </motion.div>

              <motion.div
                whileHover="hover"
                whileTap="tap"
                variants={buttonVariants}
                className="w-full sm:w-auto"
              >
                <Button
                  onClick={handleSubscribe}
                  className={cn(
                    "w-full relative overflow-hidden py-6",
                    "bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90",
                    "text-black dark:text-neutral-900 font-medium transition-all duration-200",
                    "shadow-[0_0_15px_rgba(var(--brand-gold-rgb),0.5)] hover:shadow-[0_0_20px_rgba(var(--brand-gold-rgb),0.7)]",
                    "rounded-xl"
                  )}
                  disabled={isButtonDisabled}
                >
                  {/* Shimmer effect */}
                  {isClient && !isLoading && (
                    <motion.div
                      className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none"
                      initial={{ x: "-100%" }}
                      animate={{ x: "100%" }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                    />
                  )}

                  {/* Background glow effect */}
                  {isClient && !isLoading && (
                    <div className="absolute inset-0 w-full h-full opacity-75 pointer-events-none">
                      <div className="absolute inset-0 bg-[var(--brand-gold)]/20 blur-md rounded-xl" />
                    </div>
                  )}

                  {isLoading || isProcessing ? (
                    <div className="flex items-center justify-center gap-2">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span>{isProcessing ? "Starting..." : "Processing..."}</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center gap-2">
                      <CreditCard className="w-4 h-4" />
                      <span>
                        {paymentMethodInfo?.requiresNewSubscription
                          ? "Continue with New Subscription"
                          : "Subscribe Now"}
                      </span>
                    </div>
                  )}
                </Button>
              </motion.div>
            </DialogFooter>
          </motion.div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
