"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useSubscriptionProcessing } from "../context/SubscriptionProcessingContext";
import { PricingPlan } from "@/lib/PricingPlans";
import { SubscriptionStatus } from "../page";

interface UseSubscriptionLogicProps {
  currentSubscriptionId: string | null;
  subscriptionStatus: SubscriptionStatus;
  currentPlanDetails?: PricingPlan;
  currentPlanCycle: "monthly" | "yearly";
  lastPaymentMethod?: string | null;
  razorpaySubscriptionId?: string | null;
  isEligibleForFreeTrial: boolean;
}

export function useSubscriptionLogic({
  currentSubscriptionId,
  subscriptionStatus,
  currentPlanDetails,
  currentPlanCycle,
  lastPaymentMethod,
  razorpaySubscriptionId,
  isEligibleForFreeTrial,
}: UseSubscriptionLogicProps) {
  const router = useRouter();
  const [dialogPlan, setDialogPlan] = useState<PricingPlan | null>(null);
  const [isPlanDialogOpen, setIsPlanDialogOpen] = useState(false);
  const [isFirstTimePaidPlanDialogOpen, setIsFirstTimePaidPlanDialogOpen] = useState(false);
  const [isUpiWarningDialogOpen, setIsUpiWarningDialogOpen] = useState(false);
  const [pendingSubscribeAction, setPendingSubscribeAction] = useState<(() => Promise<Response | undefined>) | null>(null);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [billingCycle, setBillingCycle] = useState<"monthly" | "yearly">(currentPlanCycle);

  const {
    completeProcessing,
    startProcessing,
    resetProcessing,
    setSubscriptionCreated,
    setFuturePaymentAuthorized
  } = useSubscriptionProcessing();

  // Handle plan selection
  const handlePlanAction = (plan: PricingPlan) => {
    console.log(`[PLAN_SWITCH_DEBUG] handlePlanAction called with plan:`, plan.id);
    console.log(`[PLAN_SWITCH_DEBUG] Current subscription status:`, subscriptionStatus);
    console.log(`[PLAN_SWITCH_DEBUG] isEligibleForFreeTrial:`, isEligibleForFreeTrial);
    console.log(`[PLAN_SWITCH_DEBUG] currentPlanDetails:`, currentPlanDetails?.id);

    // Check if this is an enterprise plan
    if (plan.id === "enterprise") {
      // For Enterprise, redirect to contact page
      router.push("/contact");
      return;
    }

    setDialogPlan(plan);

    // Check if this is a paid plan and user is eligible for free trial
    if (isEligibleForFreeTrial && plan.id !== "free" && (subscriptionStatus === "inactive" || currentPlanDetails?.id === "free")) {
      console.log(`[PLAN_SWITCH_DEBUG] Opening first-time paid plan dialog`);
      // Show the first-time paid plan dialog directly
      setIsFirstTimePaidPlanDialogOpen(true);
    } else {
      console.log(`[PLAN_SWITCH_DEBUG] Opening regular subscription dialog`);
      // Show the regular subscription dialog
      setIsPlanDialogOpen(true);
    }
  };

  // Centralized subscription logic
  const determineSubscriptionFlow = () => {
    const hasRazorpaySubscription = !!razorpaySubscriptionId;
    const paymentMethod = lastPaymentMethod?.toLowerCase() || '';
    const isCardPayment = paymentMethod === 'card';
    const isUpiOrEmandateOrUnknown = ['upi', 'emandate', 'enach', 'unknown'].includes(paymentMethod) || !lastPaymentMethod;

    return {
      hasRazorpaySubscription,
      isCardPayment,
      isUpiOrEmandateOrUnknown,
      paymentMethod
    };
  };

  // Validation checks
  const validateSubscriptionRequest = (plan: PricingPlan) => {
    if (currentSubscriptionId) {
      // Check if user is trying to switch to the same plan they already have
      if ((subscriptionStatus === "authenticated" || subscriptionStatus === "active") &&
          plan.id === currentPlanDetails?.id &&
          billingCycle === currentPlanCycle) {
        toast.error("Current Plan Selected", {
          description: "You are already subscribed to this plan. Please choose a different plan or cycle.",
        });
        completeProcessing(false, "You are already subscribed to this plan. Please choose a different plan or cycle.");
        return false;
      }

      // Handle paused/halted subscriptions
      if (subscriptionStatus === "paused" || subscriptionStatus === "halted") {
        toast.error("Subscription Paused", {
          description: "Your subscription is currently paused. Please resume your subscription before changing plans.",
        });
        completeProcessing(false, "Your subscription is currently paused. Please resume your subscription before changing plans.");
        return false;
      }
    }

    return true;
  };

  // Handle free plan subscription
  const handleFreePlanSubscription = async (_plan: PricingPlan) => {
    const { createSubscription } = await import("@/lib/actions/subscription");
    const result = await createSubscription("free", billingCycle);

    if (!result.success) {
      const errorMessage = result.error || "Failed to switch to free plan. Please try again.";
      toast.error("Subscription Error", { description: errorMessage });
      completeProcessing(false, errorMessage);
      return false;
    }

    // Show success toast
    toast.success("Plan Updated", {
      description: "Your plan has been updated to the Free plan.",
    });

    // Set subscription created status
    setSubscriptionCreated("Your plan has been updated to the Free plan.");

    // Reset dialog loading state
    setDialogLoading(false);

    // Close the dialog
    setIsPlanDialogOpen(false);

    // Refresh the page to get the latest subscription status
    setTimeout(() => {
      router.refresh();
    }, 1500);

    return true;
  };

  return {
    // State
    dialogPlan,
    isPlanDialogOpen,
    isFirstTimePaidPlanDialogOpen,
    isUpiWarningDialogOpen,
    pendingSubscribeAction,
    dialogLoading,
    billingCycle,
    
    // Setters
    setDialogPlan,
    setIsPlanDialogOpen,
    setIsFirstTimePaidPlanDialogOpen,
    setIsUpiWarningDialogOpen,
    setPendingSubscribeAction,
    setDialogLoading,
    setBillingCycle,
    
    // Functions
    handlePlanAction,
    determineSubscriptionFlow,
    validateSubscriptionRequest,
    handleFreePlanSubscription,
    
    // Processing functions
    completeProcessing,
    startProcessing,
    resetProcessing,
    setSubscriptionCreated,
    setFuturePaymentAuthorized,
  };
}
