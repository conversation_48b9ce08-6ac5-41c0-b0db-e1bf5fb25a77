/**
 * Types for business posts, customer posts, and feed functionality
 */

import { z } from "zod";

// Profile types enum (kept for future compatibility)
export const ProfileType = {
  BUSINESS: 'business',
  CUSTOMER: 'customer',
} as const;

export type ProfileTypeValue = typeof ProfileType[keyof typeof ProfileType];

// Business post schema for validation
export const postSchema = z.object({
  id: z.string().uuid().optional(),
  business_id: z.string().uuid().optional(),
  content: z.string().min(1, { message: "Post content is required" }).max(500, { message: "Post content cannot exceed 500 characters" }),
  image_url: z.union([
    z.string().url({ message: "Invalid image URL" }),
    z.literal(""),
    z.null(),
    z.undefined()
  ]).optional().nullable(),
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
  city_slug: z.string().optional(),
  state_slug: z.string().optional(),
  locality_slug: z.string().optional(),
  pincode: z.string().optional(),
  product_ids: z.array(z.string().uuid())
    .max(5, { message: "You can link up to 5 products per post" })
    .default([]),
  mentioned_business_ids: z.array(z.string().uuid())
    .max(10, { message: "You can mention up to 10 businesses per post" })
    .default([]),
});

// Type for post data from form
export type PostFormData = z.infer<typeof postSchema>;

// Type for post data from database
export interface PostData {
  id: string;
  business_id: string;
  content: string;
  image_url: string | null;
  created_at: string;
  updated_at: string;
  city_slug: string | null;
  state_slug: string | null;
  locality_slug: string | null;
  pincode: string | null;
  product_ids: string[];
  mentioned_business_ids: string[];
}

// Type for post data with business profile information
export interface PostWithBusinessProfile extends PostData {
  business_profiles?: {
    id: string;
    business_name: string | null;
    logo_url: string | null;
    business_slug: string | null;
    phone: string | null;
    whatsapp_number: string | null;
    city: string | null;
    state: string | null;
  };
}

// Type for product data in posts
export interface ProductData {
  id: string;
  name: string;
  base_price: number | null;
  discounted_price: number | null;
  image_url: string | null;
  slug: string | null;
}

// Type for post data with linked products
export interface PostWithProducts extends PostData {
  linked_products?: ProductData[];
}

// Type for feed items (currently only business posts)
export type FeedItem = PostWithBusinessProfile & Partial<PostWithProducts>;

// Type for feed filter options
export type FeedFilterType = 'smart' | 'subscribed' | 'locality' | 'pincode' | 'city' | 'state' | 'all';

// Type for feed query parameters
export interface FeedQueryParams {
  filter?: FeedFilterType;
  page?: number;
  limit?: number;
  city_slug?: string;
  state_slug?: string;
  locality_slug?: string;
  pincode?: string;
}

// ============================================================================
// CUSTOMER POSTS TYPES
// ============================================================================



// Customer post schema for validation
export const customerPostSchema = z.object({
  id: z.string().uuid().optional(),
  customer_id: z.string().uuid().optional(),
  content: z.string().min(1, { message: "Post content is required" }).max(500, { message: "Post content cannot exceed 500 characters" }),
  image_url: z.union([
    z.string().url({ message: "Invalid image URL" }),
    z.literal(""),
    z.null(),
    z.undefined()
  ]).optional().nullable(),
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
  city_slug: z.string().optional(),
  state_slug: z.string().optional(),
  locality_slug: z.string().optional(),
  pincode: z.string().optional(),
  mentioned_business_ids: z.array(z.string().uuid())
    .max(10, { message: "You can mention up to 10 businesses per post" })
    .default([]),
});

// Type for customer post data from form
export type CustomerPostFormData = z.infer<typeof customerPostSchema>;

// Type for customer post data from database
export interface CustomerPostData {
  id: string;
  customer_id: string;
  content: string;
  image_url: string | null;
  created_at: string;
  updated_at: string;
  city_slug: string | null;
  state_slug: string | null;
  locality_slug: string | null;
  pincode: string | null;
  mentioned_business_ids: string[];
}

// Type for customer post data with customer profile information
export interface CustomerPostWithProfile extends CustomerPostData {
  customer_profiles?: {
    id: string;
    name: string | null;
    avatar_url: string | null;
    city: string | null;
    state: string | null;
    locality: string | null;
  };
}

// Type for customer post data with mentioned businesses
export interface CustomerPostWithBusinesses extends CustomerPostData {
  mentioned_businesses?: {
    id: string;
    business_name: string | null;
    logo_url: string | null;
    business_slug: string | null;
    city: string | null;
    state: string | null;
  }[];
}

// Combined type for customer posts with all related data
export type CustomerFeedItem = CustomerPostWithProfile & Partial<CustomerPostWithBusinesses>;
