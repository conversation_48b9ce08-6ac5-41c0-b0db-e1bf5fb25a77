import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { getPaymentDetails, requiresNewSubscription, getPaymentMethodDisplayName } from "@/lib/razorpay/services/payment";

export async function GET(request: NextRequest) {
  try {
    // Get user from Supabase
    const supabase = await createClient();
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get subscription details from database
    const { data: subscription, error: subscriptionError } = await supabase
      .from("payment_subscriptions")
      .select("last_payment_id, last_payment_method, subscription_status, plan_id, plan_cycle")
      .eq("business_profile_id", user.id)
      .maybeSingle();

    if (subscriptionError) {
      console.error("[CHECK_PAYMENT_METHOD] Error fetching subscription:", subscriptionError);
      return NextResponse.json(
        { error: "Failed to fetch subscription details" },
        { status: 500 }
      );
    }

    if (!subscription) {
      return NextResponse.json(
        { error: "No subscription found" },
        { status: 404 }
      );
    }

    // If no last payment ID, return default flow
    if (!subscription.last_payment_id) {
      console.log("[CHECK_PAYMENT_METHOD] No last payment ID found, defaulting to card flow");
      return NextResponse.json({
        success: true,
        data: {
          paymentMethod: "card",
          displayName: "Card",
          requiresNewSubscription: false,
          canUpdateSubscription: true,
          warningMessage: null
        }
      });
    }

    // Fetch payment details from Razorpay
    const paymentResult = await getPaymentDetails(subscription.last_payment_id);

    if (!paymentResult.success || !paymentResult.data) {
      console.error("[CHECK_PAYMENT_METHOD] Failed to fetch payment details:", paymentResult.error);
      
      // Fallback to database payment method if available
      if (subscription.last_payment_method) {
        const method = subscription.last_payment_method.toLowerCase();
        const requiresNew = requiresNewSubscription(method);
        
        return NextResponse.json({
          success: true,
          data: {
            paymentMethod: method,
            displayName: getPaymentMethodDisplayName(method),
            requiresNewSubscription: requiresNew,
            canUpdateSubscription: !requiresNew,
            warningMessage: requiresNew 
              ? `Due to RBI restrictions, ${getPaymentMethodDisplayName(method)} subscriptions cannot be updated. A new subscription will be created and your previous subscription will be cancelled.`
              : null,
            source: "database" // Indicate this came from database fallback
          }
        });
      }

      // If both Razorpay and database fail, default to card
      return NextResponse.json({
        success: true,
        data: {
          paymentMethod: "card",
          displayName: "Card",
          requiresNewSubscription: false,
          canUpdateSubscription: true,
          warningMessage: null,
          source: "default"
        }
      });
    }

    // Use Razorpay payment method data
    const paymentMethod = paymentResult.data.method.toLowerCase();
    const requiresNew = requiresNewSubscription(paymentMethod);
    
    console.log("[CHECK_PAYMENT_METHOD] Payment method check result:", {
      paymentId: subscription.last_payment_id,
      paymentMethod,
      requiresNewSubscription: requiresNew
    });

    return NextResponse.json({
      success: true,
      data: {
        paymentMethod,
        displayName: getPaymentMethodDisplayName(paymentMethod),
        requiresNewSubscription: requiresNew,
        canUpdateSubscription: !requiresNew,
        warningMessage: requiresNew 
          ? `Due to RBI restrictions, ${getPaymentMethodDisplayName(paymentMethod)} subscriptions cannot be updated. A new subscription will be created and your previous subscription will be cancelled.`
          : null,
        source: "razorpay"
      }
    });

  } catch (error) {
    console.error("[CHECK_PAYMENT_METHOD] Unexpected error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
